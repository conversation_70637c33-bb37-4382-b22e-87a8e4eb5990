"""
CogBridges Search - 核心业务流程服务
实现完整的串行业务流程：Grok Reddit搜索 -> 评论者历史数据获取 -> LLM分析
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field

from config import config
from services.google_search_api import GoogleSearchService
from services.reddit_service import RedditService
from services.data_service import DataService
from services.llm_service import llm_service

from models.search_models import SearchQuery, GoogleSearchResult, SearchResult
from utils.logger_utils import get_logger
# from utils.performance_monitor import performance_monitor, PerformanceContext


@dataclass
class CogBridgesSearchResult:
    """CogBridges搜索结果"""
    query: str  # 原始查询
    session_id: str
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 翻译信息
    translated_query: str = ""  # 翻译后的查询
    translation_time: float = 0.0  # 翻译耗时
    
    # 步骤1: Grok搜索结果（保持字段名兼容性）
    google_results: List[Dict[str, Any]] = field(default_factory=list)
    google_search_time: float = 0.0
    
    # 步骤2: Reddit帖子数据（由Grok一次性获取）
    reddit_posts: List[Dict[str, Any]] = field(default_factory=list)
    reddit_posts_time: float = 0.0
    
    # 步骤3: 评论者历史数据
    commenters_history: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    commenters_history_time: float = 0.0
    
    # 步骤4: LLM分析结果（新增）
    llm_analysis: Dict[str, Any] = field(default_factory=dict)
    llm_analysis_time: float = 0.0
    
    # 总体统计
    total_time: float = 0.0
    success: bool = True
    error_message: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "query": self.query,
            "translated_query": self.translated_query,
            "session_id": self.session_id,
            "timestamp": self.timestamp.isoformat(),
            "translation_time": self.translation_time,
            "google_results": self.google_results,
            "google_search_time": self.google_search_time,
            "reddit_posts": self.reddit_posts,
            "reddit_posts_time": self.reddit_posts_time,
            "commenters_history": self.commenters_history,
            "commenters_history_time": self.commenters_history_time,
            "llm_analysis": self.llm_analysis,
            "llm_analysis_time": self.llm_analysis_time,
            "total_time": self.total_time,
            "success": self.success,
            "error_message": self.error_message
        }


class CogBridgesService:
    """CogBridges核心业务服务"""
    
    def __init__(self):
        """初始化服务"""
        self.logger = get_logger(__name__)
        
        # 初始化子服务
        self.google_service = GoogleSearchService()
        self.reddit_service = RedditService()  # 直接实例化
        self.data_service = DataService()
        self.llm_service = llm_service

        # 初始化Grok Reddit服务
        from .grok_reddit_service import GrokRedditService
        self.grok_service = GrokRedditService()

        
        # 业务参数（可配置）
        self.max_search_results = 5  # 前5个搜索结果
        self.max_comments_per_post = 6  # 每个帖子前6个评论
        self.max_user_comments = 20  # 用户历史前20个评论
        self.max_user_posts = 10  # 用户历史前10个帖子
        
        self.logger.info("CogBridges核心业务服务初始化成功")



    async def close(self):
        """关闭服务并清理资源"""
        await self.reddit_service.close()
        await self.grok_service.close()
        self.logger.info("CogBridges服务已关闭")
    
    async def search(self, query: str, save_to_db: bool = True) -> CogBridgesSearchResult:
        """
        执行完整的CogBridges搜索流程

        Args:
            query: 搜索查询
            save_to_db: 是否保存到数据库，默认为True

        Returns:
            完整的搜索结果
        """
        start_time = time.time()
        session_id = self.data_service.generate_session_id(query)
        
        result = CogBridgesSearchResult(
            query=query,
            session_id=session_id
        )
        
        try:
            self.logger.info(f"开始CogBridges搜索流程: {query}")
            
            # 步骤0: 优化查询（如果LLM服务可用）
            optimized_query = query
            optimization_time = 0.0
            
            if self.llm_service and self.llm_service.configured:
                try:
                    # 优化查询以更好地适配Google Search API（支持多语言输入，确保英文输出）
                    self.logger.info(f"步骤0: 优化查询 - {query}")
                    optimization_start = time.time()
                    optimized_query = await self.llm_service.optimize_query_for_google_search(query)
                    optimization_time = time.time() - optimization_start
                    self.logger.info(f"步骤0完成: 查询优化完成, 耗时: {optimization_time:.2f}秒")
                    
                except Exception as e:
                    self.logger.warning(f"查询优化失败，使用原查询: {e}")
                    optimized_query = query
            
            # 保存优化信息到结果中
            result.translated_query = optimized_query  # 使用最终优化后的查询
            result.translation_time = optimization_time  # 优化处理时间
            
            # 步骤1&2: 使用Grok API进行Reddit搜索（替换Google搜索和Reddit数据获取）
            grok_results = await self._step1_2_grok_reddit_search(optimized_query)
            result.google_results = grok_results.get("google_results", [])  # 保持兼容性
            result.google_search_time = grok_results["search_time"]
            result.reddit_posts = grok_results["posts"]
            result.reddit_posts_time = grok_results["search_time"]  # Grok一次性完成搜索和数据获取

            if not result.reddit_posts:
                result.success = False
                result.error_message = grok_results.get("error", "Grok Reddit搜索未找到结果")
                return result

            # 步骤3: 并行获取评论者历史数据
            commenters_data = await self._step3_get_commenters_history(result.reddit_posts)
            result.commenters_history = commenters_data["history"]
            result.commenters_history_time = commenters_data["processing_time"]
            
            # 步骤4: LLM分析（如果LLM服务可用且有评论者数据）
            llm_analysis_results = None
            llm_analysis_time = 0.0
            
            if (self.llm_service and self.llm_service.configured and 
                result.commenters_history and len(result.commenters_history) > 0):
                try:
                    self.logger.info(f"步骤4: 开始LLM分析...")
                    llm_analysis_start = time.time()
                    
                    # 执行LLM分析
                    llm_analysis_results = await self._step4_llm_analysis(result)
                    
                    llm_analysis_time = time.time() - llm_analysis_start
                    self.logger.info(f"步骤4完成: LLM分析完成, 耗时: {llm_analysis_time:.2f}秒")
                    
                except Exception as e:
                    self.logger.warning(f"LLM分析失败: {e}")
                    llm_analysis_results = {"error": str(e)}
            
            # 保存LLM分析结果
            result.llm_analysis = llm_analysis_results or {}
            result.llm_analysis_time = llm_analysis_time
            
            # 计算总时间
            result.total_time = time.time() - start_time

            # 根据参数决定是否保存完整结果
            if save_to_db:
                await self._save_results(result)

            self.logger.info(f"CogBridges搜索完成: {query}, 总耗时: {result.total_time:.2f}秒")
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            result.total_time = time.time() - start_time
            self.logger.error(f"CogBridges搜索失败: {e}")
        
        return result

    async def save_search_result_async(self, result: CogBridgesSearchResult):
        """
        异步保存搜索结果到数据库

        Args:
            result: 搜索结果对象
        """
        try:
            await self._save_results(result)
            self.logger.info(f"搜索结果异步保存成功: {result.session_id}")
        except Exception as e:
            self.logger.error(f"搜索结果异步保存失败: {e}")


    async def _step1_2_grok_reddit_search(self, query: str) -> Dict[str, Any]:
        """步骤1&2: 使用Grok API进行Reddit搜索和数据获取"""
        self.logger.info(f"步骤1&2: Grok Reddit搜索 - {query}")
        start_time = time.time()

        # 使用Grok服务搜索Reddit
        grok_result = await self.grok_service.search_reddit(
            query=query,
            max_comments=15  # 获取15条最相关的评论
        )

        if not grok_result["success"]:
            self.logger.warning(f"Grok Reddit搜索失败: {grok_result['error']}")
            return {
                "posts": [],
                "google_results": [],  # 保持兼容性
                "search_time": grok_result["search_time"],
                "error": grok_result["error"]
            }

        # 转换Grok结果为CogBridges格式
        reddit_search_result = grok_result["results"]
        converted_posts = self._convert_grok_results_to_cogbridges_format(reddit_search_result)

        search_time = time.time() - start_time
        self.logger.info(f"步骤1&2完成: Grok找到 {len(converted_posts)} 个帖子，耗时: {search_time:.2f}秒")

        return {
            "posts": converted_posts,
            "google_results": [],  # Grok直接搜索Reddit，不需要Google结果
            "search_time": search_time
        }

    def _convert_grok_results_to_cogbridges_format(self, grok_result) -> List[Dict[str, Any]]:
        """将Grok搜索结果转换为CogBridges格式"""
        converted_posts = []

        # 为每个帖子创建一个条目，包含其评论
        posts_with_comments = {}

        # 首先处理所有帖子
        for post in grok_result.posts:
            post_data = {
                "success": True,
                "post": {
                    "id": post.url.split('/')[-2] if '/' in post.url else "unknown",
                    "title": post.title,
                    "selftext": post.selftext,
                    "url": post.url,
                    "permalink": post.url,
                    "author": post.author,
                    "subreddit": post.subreddit,
                    "score": post.score,
                    "num_comments": post.num_comments,
                    "created_utc": post.created_utc
                },
                "comments": [],
                "commenters": []
            }
            posts_with_comments[post.url] = post_data

        # 然后将评论分配到对应的帖子
        for comment in grok_result.comments:
            # 尝试找到评论所属的帖子
            # 这里需要根据实际的Grok返回结构来匹配
            # 暂时为每个评论创建一个虚拟帖子条目
            comment_data = {
                "id": f"comment_{hash(comment.body)}",
                "body": comment.body,
                "author": comment.author,
                "score": comment.score,
                "created_utc": comment.created_utc,
                "permalink": comment.permalink
            }

            # 如果没有找到对应的帖子，创建一个基于评论的条目
            if not posts_with_comments:
                # 创建一个虚拟帖子来承载这个评论
                virtual_post = {
                    "success": True,
                    "post": {
                        "id": f"virtual_{hash(comment.body)}",
                        "title": f"Reddit discussion about {grok_result.search_query}",
                        "selftext": "",
                        "url": comment.author_url,
                        "permalink": comment.author_url,
                        "author": "various",
                        "subreddit": "various",
                        "score": 0,
                        "num_comments": 1,
                        "created_utc": comment.created_utc
                    },
                    "comments": [comment_data],
                    "commenters": [comment.author] if comment.author != "[deleted]" else []
                }
                converted_posts.append(virtual_post)
            else:
                # 将评论添加到第一个帖子（简化处理）
                first_post = list(posts_with_comments.values())[0]
                first_post["comments"].append(comment_data)
                if comment.author != "[deleted]":
                    first_post["commenters"].append(comment.author)

        # 添加所有有帖子数据的条目
        converted_posts.extend(posts_with_comments.values())

        return converted_posts


    
    async def _step3_get_commenters_history(self, posts_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """步骤3: 使用新的overview获取方法获取评论者历史数据"""
        self.logger.info("步骤3: 使用新的overview方法获取评论者历史数据")
        start_time = time.time()

        # 收集所有评论者
        all_commenters = set()
        for post_data in posts_data:
            all_commenters.update(post_data["commenters"])

        # 过滤有效评论者
        filtered_commenters = self._filter_valid_commenters(list(all_commenters))
        self.logger.info(f"需要获取 {len(filtered_commenters)} 个评论者的历史数据 (原始: {len(all_commenters)})")

        # 使用新的overview获取方法
        commenters_history = {}
        max_items_per_user = 100  # 每个用户最多100条记录

        # 创建并发任务
        semaphore = asyncio.Semaphore(len(filtered_commenters))  # 最大并发数等于用户数
        
        async def fetch_user_overview(username):
            async with semaphore:
                return await self.reddit_service.get_user_full_overview_history(username, max_items_per_user)

        # 并发执行所有任务
        tasks = [fetch_user_overview(username) for username in filtered_commenters]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for i, result in enumerate(results):
            username = filtered_commenters[i]
            if isinstance(result, dict) and result.get('status') == 'success':
                # 转换格式以兼容原有结构
                commenters_history[username] = self._convert_overview_to_legacy_format(result)
            elif isinstance(result, Exception):
                self.logger.warning(f"获取用户 {username} 历史失败: {result}")

        processing_time = time.time() - start_time
        self.logger.info(f"步骤3完成: 获取了 {len(commenters_history)} 个用户的历史数据, 耗时: {processing_time:.2f}秒")

        return {
            "history": commenters_history,
            "processing_time": processing_time
        }
    

    
    async def _save_results(self, result: CogBridgesSearchResult):
        """保存完整的搜索结果"""
        try:
            # 构建完整的会话数据
            complete_data = {
                "session_id": result.session_id,
                "timestamp": result.timestamp.isoformat(),
                "search_result": result.to_dict(),
                "metadata": {
                    "service_version": "2.0.0",
                    "features_enabled": {
                        "llm_analysis": bool(result.llm_analysis),
                        "translation": bool(result.translated_query and result.translated_query != result.query),
                        "enhanced_comments": True
                    }
                },
                "statistics": {
                    "google_results_count": len(result.google_results) if result.google_results else 0,
                    "reddit_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                    "commenters_count": len(result.commenters_history) if result.commenters_history else 0,
                    "similarity_analysis_count": len(result.llm_analysis.get("similarity_analysis", {})) if result.llm_analysis else 0,
                    "motivation_analysis_count": sum(len(motivations) for motivations in result.llm_analysis.get("motivation_analysis", {}).values()) if result.llm_analysis else 0,
                    "translation_time": result.translation_time,
                    "google_search_time": result.google_search_time,
                    "reddit_posts_time": result.reddit_posts_time,
                    "commenters_history_time": result.commenters_history_time,
                    "llm_analysis_time": result.llm_analysis_time,
                    "total_time": result.total_time
                },
                "business_flow_results": self._build_business_flow_results(result),
                "llm_analysis_results": {
                    "similarity_analysis": result.llm_analysis.get("similarity_analysis", {}),
                    "motivation_analysis": result.llm_analysis.get("motivation_analysis", {}),
                    "analysis_summary": result.llm_analysis.get("analysis_summary", {})
                },
                "error_info": {
                    "has_error": not result.success,
                    "error_message": result.error_message
                }
            }
            
            # 使用DataService保存完整会话数据
            search_query_obj = SearchQuery(query=result.translated_query)
            google_results_obj = [GoogleSearchResult(**res) for res in result.google_results]

            search_result_obj = SearchResult(
                query=search_query_obj,
                results=google_results_obj,
                total_results=len(google_results_obj),
                search_time=result.google_search_time,
                success=result.success,
                error_message=result.error_message
            )

            # 将search_result_obj添加到complete_data中
            complete_data["search_result_obj"] = search_result_obj.to_dict()

            filepath = self.data_service.save_complete_session(
                session_id=result.session_id,
                data_to_save=complete_data
            )
            
            self.logger.info(f"完整搜索结果已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存搜索结果失败: {e}")
    
    def _build_business_flow_results(self, result: CogBridgesSearchResult) -> Dict[str, Any]:
        """构建业务流程结果"""
        return {
            "step1_grok_search": {
                "success": bool(result.google_results),
                "results": result.google_results if result.google_results else [],
                "time_taken": result.google_search_time
            },
            "step2_reddit_posts": {
                "success": bool(result.reddit_posts),
                "total_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                "posts_summary": [
                    {
                        "title": post_data.get("post", {}).get("title", "")[:100],
                        "subreddit": post_data.get("post", {}).get("subreddit", ""),
                        "comments_count": len(post_data.get("comments", []))
                    }
                    for post_data in (result.reddit_posts if result.reddit_posts else [])
                ],
                "time_taken": result.reddit_posts_time
            },
            "step3_commenters_history": {
                "success": bool(result.commenters_history),
                "total_users_count": len(result.commenters_history) if result.commenters_history else 0,
                "users_analyzed": list(result.commenters_history.keys()) if result.commenters_history else [],
                "users_with_data": [
                    {
                        "username": username,
                        "subreddits": list(data.keys()) if isinstance(data, dict) else [],
                        "total_comments": sum(len(sub_data.get("comments", [])) for sub_data in data.values()) if isinstance(data, dict) else 0,
                        "total_posts": sum(len(sub_data.get("posts", [])) for sub_data in data.values()) if isinstance(data, dict) else 0
                    }
                    for username, data in (result.commenters_history.items() if result.commenters_history else [])
                    if isinstance(data, dict) and any(sub_data.get("comments") or sub_data.get("posts") for sub_data in data.values())
                ][:10],  # 限制显示前10个有数据的用户
                "time_taken": result.commenters_history_time
            },
            "step4_llm_analysis": {
                "success": bool(result.llm_analysis),
                "credibility_analysis_count": len(result.llm_analysis.get("credibility_analysis", {})) if result.llm_analysis else 0,
                "analysis_summary": result.llm_analysis.get("analysis_summary", {}),
                "time_taken": result.llm_analysis_time
            }
        }

    def get_statistics(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        stats = {
            "google_stats": self.google_service.get_statistics(),
            "reddit_stats": self.reddit_service.get_statistics(),
            "business_config": {
                "max_search_results": self.max_search_results,
                "max_comments_per_post": self.max_comments_per_post,
                "max_user_comments": self.max_user_comments,
                "max_user_posts": self.max_user_posts
            }
        }

        # 添加LLM服务统计（如果可用）
        if self.llm_service.configured:
            stats["llm_stats"] = self.llm_service.get_stats()

        return stats

    def _filter_valid_commenters(self, commenters: List[str]) -> List[str]:
        """
        过滤有效的评论者，移除机器人账户和无效用户

        Args:
            commenters: 原始评论者列表

        Returns:
            过滤后的评论者列表
        """
        import re

        filtered = []

        # 常见的机器人账户模式
        bot_patterns = [
            r'.*bot$',
            r'.*_bot$',
            r'bot_.*',
            r'auto.*',
            r'.*moderator.*',
            r'.*admin.*'
        ]

        # 无效用户名模式
        invalid_patterns = [
            r'^\[deleted\]$',
            r'^\[removed\]$',
            r'^deleted$',
            r'^removed$'
        ]

        for commenter in commenters:
            if not commenter or len(commenter.strip()) == 0:
                continue

            commenter_lower = commenter.lower()

            # 检查是否为无效用户
            is_invalid = any(re.match(pattern, commenter_lower) for pattern in invalid_patterns)
            if is_invalid:
                continue

            # 检查是否为机器人（可选，根据需要启用）
            # is_bot = any(re.match(pattern, commenter_lower) for pattern in bot_patterns)
            # if is_bot:
            #     continue

            # 过滤过短或过长的用户名
            if len(commenter) < 3 or len(commenter) > 20:
                continue

            filtered.append(commenter)

        return filtered

    async def _get_user_full_overview_history(self, username: str, max_items: int = 100) -> Dict[str, Any]:
        """获取单个用户的所有历史overview（帖子和评论）"""
        try:
            reddit = await self.reddit_service._ensure_async_reddit()
            redditor = await reddit.redditor(username)
            
            submissions = []
            comments = []
            total_count = 0
            start_time = time.time()
            
            # 获取所有历史overview（包括帖子和评论），按top排序
            async for item in redditor.top(limit=None):
                total_count += 1
                
                # 判断是帖子还是评论
                if hasattr(item, 'is_self') or hasattr(item, 'title'):  # 这是帖子
                    submission_info = await self._get_submission_info(item)
                    submissions.append(submission_info)
                else:  # 这是评论
                    comment_info = await self._get_comment_info(item)
                    # 只保留直接回复帖子的评论
                    if comment_info.get('is_reply_to_submission', False):
                        comments.append(comment_info)
                
                # 设置安全限制，避免无限循环
                if total_count >= max_items:
                    break
            
            end_time = time.time()
            total_time = end_time - start_time
            
            return {
                "username": username,
                "status": "success",
                "total_items": total_count,
                "submissions_count": len(submissions),
                "comments_count": len(comments),
                "total_time": total_time,
                "rate_per_second": total_count/total_time if total_time > 0 else 0,
                "submissions": submissions,
                "comments": comments,
                "reached_limit": total_count >= max_items
            }
            
        except Exception as e:
            self.logger.warning(f"获取用户 {username} overview失败: {e}")
            return {
                "username": username,
                "status": "error",
                "error": str(e),
                "total_items": 0,
                "submissions_count": 0,
                "comments_count": 0,
                "total_time": 0,
                "rate_per_second": 0,
                "submissions": [],
                "comments": [],
                "reached_limit": False
            }

    async def _get_submission_info(self, submission) -> Dict[str, Any]:
        """获取帖子的基本信息"""
        try:
            return {
                "score": getattr(submission, 'score', 0),
                "title": getattr(submission, 'title', 'N/A'),
                "selftext": getattr(submission, 'selftext', 'N/A'),
                "subreddit": getattr(submission, 'subreddit_name_prefixed', 'N/A'),
                "created_utc": getattr(submission, 'created_utc', 0)
            }
        except Exception as e:
            return {
                "error": f"获取帖子信息失败: {str(e)}",
                "score": 0,
                "title": "N/A",
                "selftext": "N/A",
                "subreddit": "N/A",
                "created_utc": 0
            }

    async def _get_comment_info(self, comment) -> Dict[str, Any]:
        """获取评论的基本信息"""
        try:
            # 判断是回复帖子还是评论
            parent_id = getattr(comment, 'parent_id', 'N/A')
            is_reply_to_submission = parent_id.startswith('t3_') if parent_id != 'N/A' else False
            
            return {
                "body": getattr(comment, 'body', 'N/A'),
                "score": getattr(comment, 'score', 0),
                "created_utc": getattr(comment, 'created_utc', 0),
                "subreddit": getattr(comment, 'subreddit_name_prefixed', 'N/A'),
                "is_reply_to_submission": is_reply_to_submission,
                "submission_title": getattr(comment, 'link_title', 'N/A')
            }
        except Exception as e:
            return {
                "error": f"获取评论信息失败: {str(e)}",
                "body": "N/A",
                "score": 0,
                "created_utc": 0,
                "subreddit": "N/A",
                "is_reply_to_submission": False,
                "submission_title": "N/A"
            }

    def _convert_overview_to_legacy_format(self, overview_result: Dict[str, Any]) -> Dict[str, Any]:
        """将新的overview格式转换为原有的legacy格式"""
        if overview_result.get('status') != 'success':
            return {}
        
        legacy_format = {}
        
        # 收集所有子版块
        all_subreddits = []
        all_subreddits.extend([s.get('subreddit', 'N/A') for s in overview_result.get('submissions', []) if s.get('subreddit') != 'N/A'])
        all_subreddits.extend([c.get('subreddit', 'N/A') for c in overview_result.get('comments', []) if c.get('subreddit') != 'N/A'])
        unique_subreddits = list(set(all_subreddits))
        
        # 处理帖子数据
        for submission in overview_result.get('submissions', []):
            subreddit = submission.get('subreddit', 'N/A')
            if subreddit not in legacy_format:
                legacy_format[subreddit] = {"posts": [], "comments": []}
            
            legacy_format[subreddit]["posts"].append({
                "id": f"post_{submission.get('created_utc', 0)}",  # 生成唯一ID
                "title": submission.get('title', 'N/A'),
                "score": submission.get('score', 0),
                "created_utc": submission.get('created_utc', 0)
            })
        
        # 处理评论数据
        for comment in overview_result.get('comments', []):
            subreddit = comment.get('subreddit', 'N/A')
            if subreddit not in legacy_format:
                legacy_format[subreddit] = {"posts": [], "comments": []}
            
            legacy_format[subreddit]["comments"].append({
                "id": f"comment_{comment.get('created_utc', 0)}",  # 生成唯一ID
                "body": comment.get('body', 'N/A')[:200] + "..." if len(comment.get('body', '')) > 200 else comment.get('body', 'N/A'),
                "score": comment.get('score', 0),
                "created_utc": comment.get('created_utc', 0)
            })
        
        # 添加子版块列表信息
        legacy_format["_metadata"] = {
            "subreddits": unique_subreddits,
            "unique_subreddits_count": len(unique_subreddits),
            "total_posts": len(overview_result.get('submissions', [])),
            "total_comments": len(overview_result.get('comments', [])),
            "username": overview_result.get('username', ''),
            "total_items": overview_result.get('total_items', 0),
            "rate_per_second": overview_result.get('rate_per_second', 0)
        }
        
        return legacy_format

    async def _step4_llm_analysis(self, result: CogBridgesSearchResult) -> Dict[str, Any]:
        """步骤4: 执行LLM分析功能"""
        llm_results = {
            "similarity_analysis": {},
            "motivation_analysis": {},
            "analysis_summary": {},
            "success": False,
            "error": None
        }
        
        try:
            # 检查LLM服务配置
            if not self.llm_service.configured:
                llm_results["error"] = "LLM服务未配置"
                return llm_results
            
            # 提取用户和subreddit信息
            users_data = self._extract_users_and_subreddits(result)
            self.logger.info(f"提取到 {len(users_data)} 个用户数据")
            
            if not users_data:
                llm_results["error"] = "未找到有效的用户数据"
                return llm_results
            
            # 执行评论者可信度分析
            self.logger.info("执行评论者可信度分析...")
            credibility_results = await self._analyze_commenter_credibility(users_data, result)
            llm_results["credibility_analysis"] = credibility_results

            # 生成分析总结
            llm_results["analysis_summary"] = self._generate_credibility_analysis_summary(credibility_results)
            llm_results["success"] = True
            
            return llm_results
            
        except Exception as e:
            self.logger.error(f"LLM分析失败: {e}")
            llm_results["error"] = str(e)
            return llm_results
    
    def _extract_users_and_subreddits(self, search_result: CogBridgesSearchResult) -> List[Dict[str, Any]]:
        """从搜索结果中提取用户和subreddit信息"""
        users_data = []
        
        if not search_result.commenters_history:
            return users_data
        
        for username, user_data in search_result.commenters_history.items():
            if not isinstance(user_data, dict):
                continue
                
            # 获取用户的subreddits列表
            user_subreddits = []
            if '_metadata' in user_data:
                metadata = user_data['_metadata']
                user_subreddits = metadata.get('subreddits', [])
            
            # 如果没有metadata，从用户数据中提取subreddits
            if not user_subreddits:
                user_subreddits = [sr for sr in user_data.keys() if sr != '_metadata']
            
            if user_subreddits:
                users_data.append({
                    "username": username,
                    "user_subreddits": user_subreddits,
                    "user_data": user_data
                })
        
        return users_data
    
    async def _analyze_commenter_credibility(self, users_data: List[Dict[str, Any]], search_result: CogBridgesSearchResult) -> Dict[str, Any]:
        """分析评论者可信度"""
        credibility_results = {}

        # 构建评论者可信度分析任务
        tasks = []
        for user_info in users_data:
            username = user_info["username"]
            user_data = user_info["user_data"]

            # 获取用户的高赞历史评论和帖子
            high_score_content = self._extract_high_score_content(user_data)

            if high_score_content:
                tasks.append(self._analyze_single_commenter_credibility(
                    username, high_score_content
                ))

        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            credibility_results = self._process_credibility_results(results)

        return credibility_results
    
    def _extract_high_score_content(self, user_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取用户的高赞内容（帖子和评论）"""
        high_score_content = []

        for subreddit, sub_data in user_data.items():
            # 提取高赞帖子
            posts = sub_data.get("posts", [])
            for post in posts:
                if post.get("score", 0) > 10:  # 只取得分大于10的帖子
                    high_score_content.append({
                        "type": "post",
                        "content": post.get("title", "") + " " + post.get("selftext", ""),
                        "score": post.get("score", 0),
                        "subreddit": subreddit,
                        "created_utc": post.get("created_utc", 0)
                    })

            # 提取高赞评论
            comments = sub_data.get("comments", [])
            for comment in comments:
                if comment.get("score", 0) > 5:  # 只取得分大于5的评论
                    high_score_content.append({
                        "type": "comment",
                        "content": comment.get("body", ""),
                        "score": comment.get("score", 0),
                        "subreddit": subreddit,
                        "created_utc": comment.get("created_utc", 0)
                    })

        # 按得分排序，取前20条
        high_score_content.sort(key=lambda x: x["score"], reverse=True)
        return high_score_content[:20]
    
    def _process_credibility_results(self, results: List[Any]) -> Dict[str, Any]:
        """处理可信度分析结果"""
        credibility_results = {}

        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"可信度分析任务失败: {result}")
                continue

            if not isinstance(result, dict) or "username" not in result:
                continue

            username = result["username"]
            credibility_results[username] = result

        return credibility_results
    
    async def _analyze_single_commenter_credibility(self, username: str, high_score_content: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析单个评论者的可信度"""
        try:
            # 提取活跃社区信息
            active_subreddits = list(set(content.get("subreddit", "") for content in high_score_content if content.get("subreddit")))

            # 调用LLM分析评论者画像
            result = await self.llm_service.analyze_commenter_credibility(
                username=username,
                high_score_content=high_score_content
            )

            # 添加活跃社区信息
            if "subreddits_active_in" not in result or not result["subreddits_active_in"]:
                result["subreddits_active_in"] = active_subreddits[:10]  # 限制最多10个

            return {
                "username": username,
                **result
            }

        except Exception as e:
            self.logger.error(f"用户{username}的可信度分析失败: {e}")
            return {
                "username": username,
                "error": str(e)
            }
    
    def _generate_credibility_analysis_summary(self, credibility_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成可信度分析总结"""
        summary = {
            "total_users_analyzed": len(credibility_results),
            "successful_analyses": 0,
            "failed_analyses": 0,
            "users_with_expertise": 0,
            "users_with_background_info": 0,
            "users_with_worldview_info": 0
        }

        for username, result in credibility_results.items():
            if "error" in result:
                summary["failed_analyses"] += 1
            else:
                summary["successful_analyses"] += 1

                # 检查是否有专业性信息
                if result.get("expertise", {}).get("summary"):
                    summary["users_with_expertise"] += 1

                # 检查是否有背景信息
                if result.get("background_similarity", {}).get("summary"):
                    summary["users_with_background_info"] += 1

                # 检查是否有世界观信息
                if result.get("worldview", {}).get("summary"):
                    summary["users_with_worldview_info"] += 1

        return summary
    

    

    

