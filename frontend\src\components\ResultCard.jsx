import { useState } from 'react'
import { User, MessageCircle, Heart, ExternalLink, Eye, Bookmark, Calendar, FileText } from 'lucide-react'

const ResultCard = ({ result, onViewDetail, onBookmark }) => {
  const [isBookmarked, setIsBookmarked] = useState(false)

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked)
    onBookmark?.(result, !isBookmarked)
  }

  // 格式化时间
  const formatTime = (timestamp) => {
    if (!timestamp) return '未知时间'
    try {
      // 如果是Unix时间戳
      const date = new Date(timestamp * 1000)
      if (isNaN(date.getTime())) {
        // 如果不是有效的时间戳，尝试直接解析
        const directDate = new Date(timestamp)
        if (isNaN(directDate.getTime())) return '未知时间'
        return directDate.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      }
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (e) {
      return '未知时间'
    }
  }

  const getPersonaTags = (insights) => {
    // 根据洞察内容生成标签
    const tags = []
    if (insights?.includes('经验丰富')) tags.push('亲身经历')
    if (insights?.includes('理性分析')) tags.push('理性思考')
    if (insights?.includes('情感支持')) tags.push('温暖陪伴')
    if (insights?.includes('专业')) tags.push('专业见解')
    return tags.length > 0 ? tags : ['深度思考']
  }

  // 获取帖子信息
  const post = result._rawData?.post || {}
  const postTitle = post.title || '相关讨论'
  const postSelftext = post.selftext || ''
  const postCreatedTime = post.created_utc

  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-gray-200">
      {/* 帖子信息 */}
      <div className="mb-4 p-4 bg-gray-50 rounded-lg border-l-4 border-primary-400">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-800 flex-1">
            <FileText className="w-5 h-5 inline mr-2 text-primary-600" />
            {postTitle}
          </h3>
        </div>

        {postSelftext && (
          <div className="text-sm text-gray-600 mb-2 line-clamp-3">
            {postSelftext.length > 200 ? `${postSelftext.substring(0, 200)}...` : postSelftext}
          </div>
        )}

        <div className="flex items-center text-xs text-gray-500 space-x-4">
          <span className="flex items-center">
            <Calendar className="w-3 h-3 mr-1" />
            发布于 {formatTime(postCreatedTime)}
          </span>
          <span className="flex items-center">
            <MessageCircle className="w-3 h-3 mr-1" />
            来自 r/{result.subreddit}
          </span>
        </div>
      </div>

      {/* 评论内容 */}
      <div className="mb-4">
        <div className="text-sm text-gray-500 mb-2 font-medium">
          💬 相关回答：
        </div>
        <div className="text-lg font-medium text-gray-800 mb-2 leading-relaxed bg-blue-50 p-3 rounded-lg">
          <div className="whitespace-pre-wrap">{result.fullComment || result.comment}</div>
        </div>
        <div className="flex items-center text-sm text-gray-500 space-x-4">
          <span className="flex items-center">
            <Heart className="w-4 h-4 mr-1" />
            {result.score} 赞
          </span>
          <span className="flex items-center">
            <User className="w-4 h-4 mr-1" />
            u/{result.author}
          </span>
          {/* 显示数据来源 */}
          <span className="flex items-center text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">
            Grok AI 搜索
          </span>
        </div>
      </div>

      {/* 用户背景洞察 */}
      <div className="mb-4 p-3 bg-green-50 rounded-lg border-l-4 border-green-400">
        <div className="text-sm text-gray-600">
          <p className="mb-2">
            <strong className="text-green-700">💡 用户背景洞察:</strong> {result.insights || "该用户在相关领域有丰富经验，发言理性客观"}
          </p>
        </div>
      </div>

      {/* 观点标签 */}
      <div className="mb-4">
        <div className="flex flex-wrap gap-2">
          {getPersonaTags(result.insights).map((tag, index) => (
            <span
              key={index}
              className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium"
            >
              #{tag}
            </span>
          ))}
        </div>
      </div>

      {/* 推荐理由 */}
      <div className="mb-4 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
        <p className="text-sm text-blue-800">
          <strong>为什么推荐:</strong> {result.recommendation || "该回答基于真实经历，观点平衡理性，能提供有价值的参考"}
        </p>
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
        <button
          onClick={() => onViewDetail?.(result)}
          className="flex items-center px-4 py-2 text-primary-600 hover:text-primary-700 hover:bg-primary-50 rounded-lg transition-colors"
        >
          <Eye className="w-4 h-4 mr-2" />
          查看详情
        </button>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleBookmark}
            className={`p-2 rounded-lg transition-colors ${
              isBookmarked 
                ? 'text-yellow-600 bg-yellow-50 hover:bg-yellow-100' 
                : 'text-gray-400 hover:text-yellow-600 hover:bg-yellow-50'
            }`}
            title={isBookmarked ? '取消收藏' : '收藏'}
          >
            <Bookmark className={`w-4 h-4 ${isBookmarked ? 'fill-current' : ''}`} />
          </button>

          <a
            href={result.url}
            target="_blank"
            rel="noopener noreferrer"
            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="查看原帖"
          >
            <ExternalLink className="w-4 h-4" />
          </a>
        </div>
      </div>
    </div>
  )
}

export default ResultCard 