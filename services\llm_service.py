"""
CogBridges Search - LLM服务
实现Replicate API调用，支持参数和prompt管理
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Union
import replicate
from tenacity import retry, stop_after_attempt, wait_exponential

from config import config
from utils.logger_utils import get_logger, log_api_call



class LLMService:
    """LLM服务类，支持Replicate API调用"""
    
    def __init__(self):
        """初始化LLM服务"""
        self.logger = get_logger(__name__)
        
        # 检查配置
        self.configured = config.replicate_configured
        if not self.configured:
            self.logger.warning("Replicate API未配置，LLM服务不可用")
            return
        

        
        # 初始化Replicate客户端
        self.client = replicate.Client(api_token=config.REPLICATE_API_TOKEN)
        
        # 请求统计
        self.request_count = 0
        self.total_request_time = 0.0
        
        self.logger.info("LLM服务初始化成功")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @log_api_call("Replicate", "run", "POST")
    async def generate_text(
        self,
        prompt: str,
        model: str = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        system_prompt: str = None,
        **kwargs
    ) -> Optional[str]:
        """
        生成文本
        
        Args:
            prompt: 用户提示
            model: 模型名称，默认使用配置中的模型
            max_tokens: 最大token数
            temperature: 温度参数
            system_prompt: 系统提示
            **kwargs: 其他参数
            
        Returns:
            生成的文本
        """
        if not self.configured:
            raise ValueError("Replicate API未配置，无法使用LLM服务")
        
        start_time = time.time()
        model = model or config.REPLICATE_MODEL
        
        try:
            # 构建输入参数
            input_data = {
                "prompt": prompt,
                "max_tokens": max_tokens,
                "temperature": temperature,
                **kwargs
            }
            
            # 添加系统提示（如果支持）
            if system_prompt:
                input_data["system_prompt"] = system_prompt
            
            self.logger.debug(f"调用LLM模型: {model}, prompt长度: {len(prompt)}")
            
            # 调用Replicate API
            output = await asyncio.to_thread(
                self.client.run,
                model,
                input=input_data
            )
            
            # 处理输出
            if isinstance(output, list):
                result = "".join(str(item) for item in output)
            else:
                result = str(output)
            
            # 更新统计
            self.request_count += 1
            self.total_request_time += time.time() - start_time
            
            self.logger.info(f"LLM调用成功，输出长度: {len(result)}")
            return result
            
        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            raise
    

    
    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "configured": self.configured,
            "request_count": self.request_count,
            "total_request_time": self.total_request_time,
            "average_request_time": (
                self.total_request_time / self.request_count 
                if self.request_count > 0 else 0
            )
        }
    
    async def test_connection(self) -> bool:
        """测试Replicate API连接"""
        if not self.configured:
            return False

        try:
            # 简单的测试调用
            result = await self.generate_text(
                prompt="Hello",
                max_tokens=10,
                temperature=0.1
            )
            if result:
                self.logger.info("Replicate API连接测试成功")
                return True
            else:
                self.logger.error("Replicate API连接测试失败: 无响应")
                return False
        except Exception as e:
            self.logger.error(f"Replicate API连接测试失败: {e}")
            return False

    async def filter_similar_subreddits_batch(
        self,
        user_subreddits: List[str],
        target_subreddits: List[str],
        user_id: str = None
    ) -> Dict[str, List[str]]:
        """
        批量筛选用户subreddits与多个目标subreddit的相似性
        
        Args:
            user_subreddits: 用户关注的subreddits列表
            target_subreddits: 目标subreddit列表
            user_id: 用户ID（用于日志记录）
            
        Returns:
            每个目标subreddit对应的相似subreddits字典
        """
        if not user_subreddits or not target_subreddits:
            self.logger.warning(f"用户{user_id}的subreddit列表或目标subreddit列表为空")
            return {}
        
        # 记录筛选开始
        self.logger.info(f"开始批量筛选用户{user_id}的相似subreddits - "
                        f"目标subreddits: {target_subreddits}, 用户subreddits数量: {len(user_subreddits)}")
        
        try:
            # 构建批量筛选提示
            prompt = self._build_batch_similarity_filter_prompt(user_subreddits, target_subreddits)
            
            # 调用LLM进行批量筛选
            response = await self.generate_text(
                prompt=prompt,
                temperature=0.3,  # 较低温度确保一致性
                max_tokens=1000
            )
            
            # 记录LLM原始响应（用于调试）
            self.logger.debug(f"LLM原始响应: {response}")
            
            # 解析批量筛选结果
            similar_subreddits_dict = self._parse_batch_similarity_filter_result(response, user_subreddits, target_subreddits)
            
            # 记录筛选完成
            total_similar = sum(len(similar_list) for similar_list in similar_subreddits_dict.values())
            self.logger.info(f"批量相似subreddits筛选完成 - "
                           f"用户ID: {user_id}, 总筛选出相似subreddits数量: {total_similar}")
            
            return similar_subreddits_dict
            
        except Exception as e:
            self.logger.error(f"批量相似subreddits筛选失败 - 用户ID: {user_id}, 错误: {e}")
            return {}





    def _build_batch_similarity_filter_prompt(
        self,
        user_subreddits: List[str],
        target_subreddits: List[str]
    ) -> str:
        """构建批量相似性筛选提示"""
        user_subreddit_list = ", ".join(user_subreddits)
        target_subreddit_list = ", ".join(target_subreddits)
        
        prompt = f"""请从以下用户关注的Reddit子版块中，筛选出与每个目标子版块具有内容相似性的子版块。

用户关注的子版块：{user_subreddit_list}

目标子版块：{target_subreddit_list}

请为每个目标子版块分别筛选相似的子版块，并按照以下JSON格式返回结果：

{{
  "singularity": ["subreddit1", "subreddit2"],
  "ClaudeAI": ["subreddit3", "subreddit4"],
  "grok": ["subreddit5", "subreddit6"]
}}

重要提示：
1. 如果用户关注的子版块中直接包含目标子版块（忽略大小写和r/前缀），请一定要包含它
2. 例如：如果目标是"singularity"，用户有"r/singularity"，那么"r/singularity"必须被包含在结果中
3. 如果某个目标子版块没有相似的子版块，请返回空数组 []

筛选原则（请宽松判断，考虑多种关联性）：
1. 主题相关性：相同或相近的主题领域
2. 用户群体重叠：目标用户群体相似
3. 内容类型相似：讨论内容、风格、深度相似
4. 技术栈关联：技术工具、平台、语言的关联
5. 兴趣领域重叠：用户兴趣的交叉领域
6. 应用场景相似：使用场景、应用领域的相似性
7. 知识体系相关：知识结构、学习路径的关联
8. 行业相关性：同一行业或相关行业
9. 技能要求相似：所需技能、专业知识的重叠
10. 发展趋势相关：发展方向、趋势的相似性

特别注意：
- 对于技术类subreddit，编程、开发、数据相关的通常都与技术工具相关
- 对于娱乐类subreddit，游戏、影视、音乐等通常都有娱乐属性
- 对于学习类subreddit，教育、技能、知识相关的通常都与学习相关
- 对于生活类subreddit，健康、美食、旅行等通常都与生活方式相关
- 对于商业类subreddit，投资、创业、管理相关的通常都与商业相关

请采用宽松的匹配策略，只要有一定相关性就包含进来。

请确保返回的是有效的JSON格式。"""
        
        return prompt

    def _parse_batch_similarity_filter_result(
        self,
        response: str,
        original_subreddits: List[str],
        target_subreddits: List[str]
    ) -> Dict[str, List[str]]:
        """解析批量相似性筛选结果"""
        try:
            import json
            import re
            
            # 清理响应文本
            response = response.strip()
            
            # 移除markdown代码块标记
            response = re.sub(r'```json\s*', '', response)
            response = re.sub(r'```\s*$', '', response)
            response = response.strip()
            
            # 尝试解析JSON
            try:
                result_dict = json.loads(response)
            except json.JSONDecodeError:
                self.logger.error(f"LLM响应不是有效的JSON格式: {response}")
                return {}
            
            # 创建原始subreddit的映射（去掉r/前缀）
            original_mapping = {}
            for sr in original_subreddits:
                clean_name = sr.replace('r/', '').lower()
                original_mapping[clean_name] = sr
            
            # 处理每个目标subreddit的结果
            final_result = {}
            for target_subreddit in target_subreddits:
                similar_list = result_dict.get(target_subreddit, [])
                if not isinstance(similar_list, list):
                    continue
                
                # 验证和转换subreddit名称
                validated_similar = []
                for sr in similar_list:
                    sr_str = str(sr).strip()
                    
                    # 尝试直接匹配
                    if sr_str in original_subreddits:
                        validated_similar.append(sr_str)
                    # 尝试去掉r/前缀后匹配
                    elif sr_str in original_mapping:
                        validated_similar.append(original_mapping[sr_str])
                    # 尝试添加r/前缀后匹配
                    elif f"r/{sr_str}" in original_subreddits:
                        validated_similar.append(f"r/{sr_str}")
                
                final_result[target_subreddit] = validated_similar
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"解析批量相似性筛选结果失败: {e}")
            return {}



    async def analyze_user_comment_motivation(
        self,
        user_id: str,
        target_subreddit: str,
        target_post: Dict[str, Any],
        user_comment: Dict[str, Any],
        similar_subreddits_data: List[Dict[str, Any]] = None,
        user_overview: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        分析用户在目标subreddit中评论的动机和背景
        
        Args:
            user_id: 用户ID
            target_subreddit: 目标subreddit（如Claude）
            target_post: 目标帖子的详细信息
            user_comment: 用户的评论内容
            similar_subreddits_data: 相似subreddits的帖子和评论数据
            user_overview: 用户概览信息
            
        Returns:
            用户评论动机分析结果
        """
        # 记录分析开始
        self.logger.info(f"开始分析用户{user_id}在{target_subreddit}中的评论动机")
        
        try:
            # 构建分析提示
            prompt = self._build_comment_motivation_prompt(
                user_id, target_subreddit, target_post, user_comment, 
                similar_subreddits_data, user_overview
            )
            
            # 调用LLM进行分析
            response = await self.generate_text(
                prompt=prompt,
                temperature=0.4,  # 适中的温度，平衡创造性和一致性
                max_tokens=1000
            )
            
            # 解析分析结果
            result = self._parse_comment_motivation_result(response, user_id, target_subreddit)
            
            # 记录分析完成
            self.logger.info(f"用户{user_id}评论动机分析完成")
            
            return result
            
        except Exception as e:
            self.logger.error(f"用户{user_id}评论动机分析失败: {e}")
            return {
                "user_id": user_id,
                "target_subreddit": target_subreddit,
                "analysis": f"分析失败: {str(e)}",
                "error": str(e)
            }

    def _build_comment_motivation_prompt(
        self,
        user_id: str,
        target_subreddit: str,
        target_post: Dict[str, Any],
        user_comment: Dict[str, Any],
        similar_subreddits_data: List[Dict[str, Any]] = None,
        user_overview: Dict[str, Any] = None
    ) -> str:
        """构建评论动机分析提示"""
        
        # 构建目标帖子信息
        post_info = f"""
目标帖子信息：
- 标题：{target_post.get('title', 'N/A')}
- 作者：{target_post.get('author', 'N/A')}
- 内容：{target_post.get('selftext', 'N/A')[:500]}...
- 评分：{target_post.get('score', 0)}
- 评论数：{target_post.get('num_comments', 0)}
"""
        
        # 构建用户评论信息
        comment_info = f"""
用户评论信息：
- 评论内容：{user_comment.get('body', 'N/A')}
- 评论评分：{user_comment.get('score', 0)}
- 评论时间：{user_comment.get('created_utc', 'N/A')}
"""
        
        # 构建相似subreddits数据
        similar_data_info = ""
        if similar_subreddits_data:
            similar_data_info = "\n相似subreddits相关数据：\n"
            for i, data in enumerate(similar_subreddits_data[:3]):  # 限制数量避免过长
                similar_data_info += f"""
相似subreddit {i+1}：{data.get('subreddit', 'N/A')}
- 相关帖子数量：{len(data.get('posts', []))}
- 相关评论数量：{len(data.get('comments', []))}
- 用户参与度：{data.get('user_engagement', 'N/A')}
"""
        
        # 构建用户概览信息
        user_info = ""
        if user_overview:
            user_info = f"""
用户概览信息：
- 用户ID：{user_id}
- 关注subreddits：{', '.join(user_overview.get('subreddits', []))}
- 用户类型：{user_overview.get('user_type', 'N/A')}
- 活跃度：{user_overview.get('activity_level', 'N/A')}
"""
        
        prompt = f"""请分析用户{user_id}在{target_subreddit}子版块中发表评论的动机和背景。

{post_info}

{comment_info}

{similar_data_info}

{user_info}

请从以下角度分析用户的评论动机：

1. 专业背景分析：
   - 用户可能的技术背景或专业领域
   - 与目标subreddit主题的相关性
   - 专业经验水平判断

2. 参与动机分析：
   - 用户评论的主要目的（分享经验、寻求帮助、表达观点等）
   - 情感倾向（积极、消极、中性）
   - 参与深度（浅层参与、深度讨论）

3. 兴趣领域分析：
   - 用户对相关技术的兴趣程度
   - 在相似subreddits中的参与模式
   - 知识结构和技能水平

4. 用户画像推断：
   - 可能的职业身份
   - 技术熟练程度
   - 学习阶段或经验水平
   - 使用AI工具的目的和场景

5. 潜在匹配价值：
   - 作为成长伙伴的潜在价值
   - 可能的合作或交流机会
   - 知识互补性分析

请按照以下格式返回分析结果：

专业背景：[分析用户的专业背景]
参与动机：[分析用户的参与动机]
兴趣领域：[分析用户的兴趣领域]
用户画像：[推断的用户画像]
匹配价值：[潜在匹配价值分析]
总体评价：[简要总结]

请确保分析客观、准确，基于提供的数据进行合理推断。"""
        
        return prompt

    async def optimize_query_for_google_search(self, query: str) -> str:
        """
        优化搜索查询以更好地适配Google Search API，支持多语言输入，确保英文输出
        
        Args:
            query: 原始查询字符串（支持多语言）
            
        Returns:
            优化后的英文查询字符串
        """
        if not self.configured:
            self.logger.warning("LLM服务未配置，跳过查询优化")
            return query
        
        try:
            # 构建查询优化提示
            optimization_prompt = f"""
You are an AI assistant tasked with optimizing queries for the Google Search API. Your goal is to generate an effective search query based on the user's search intent, using Google search operators to retrieve the most accurate and relevant results. **Regardless of the input language, the generated search query must always be in English** to ensure compatibility with the Google Search API and broad applicability of results.

Original Query: {query}

When constructing the search query, consider using the following search operators when applicable:
- **Quotes (" ")**: Search for an exact phrase. For example, `"machine learning"` finds pages containing the exact phrase.
- **Minus (-)**: Exclude specific terms. For example, `jaguar -car` finds pages about the animal, excluding car-related content.
- **OR**: Search for pages containing either term. For example, `vacation OR trip`.
- **site:**: Restrict search to a specific website. For example, `site:reddit.com` searches only reddit.com.
- **filetype:**: Find specific file types. For example, `filetype:pdf` finds PDF files.
- **intitle:**: Search for pages with a specific word in the title. For example, `intitle:guide`.
- **inurl:**: Search for pages with a specific word in the URL. For example, `inurl:blog`.

Ensure the search query is clear, specific, and meets the user's needs. If the user provides input in a non-English language, translate their intent into English and generate the corresponding English query. For example, a Chinese input like "人工智能新闻" should yield `"artificial intelligence" news`.

Here are some example guidelines:
1. **User Intent**: Find recent news articles about artificial intelligence (input language: any, e.g., Chinese "人工智能新闻").
   **Optimized Query**: `"artificial intelligence" news`
2. **User Intent**: Search for Python programming tutorials, excluding videos (input language: any, e.g., Spanish "tutoriales de programación en Python sin videos").
   **Optimized Query**: `python programming tutorial -video`
3. **User Intent**: Find PDF documents on climate change from government websites (input language: any, e.g., French "changement climatique documents PDF gouvernement").
   **Optimized Query**: `climate change site:gov filetype:pdf`
4. **User Intent**: Find pages mentioning "blockchain" and "cryptocurrency" but not "bitcoin" (input language: any, e.g., Japanese "ブロックチェーン 暗号通貨 ビットコインなし").
   **Optimized Query**: `blockchain AND cryptocurrency -bitcoin`

Use logical operators like AND, OR, NOT appropriately to combine terms. The generated query must be in English, concise, and compliant with Google Search API syntax.

Your goal is to generate an English search query that, when used with the Google Search API, returns the most relevant and accurate results.

Optimized English Query:
"""
            
            # 调用LLM进行优化
            optimized_query = await self.generate_text(
                prompt=optimization_prompt,
                max_tokens=200,  # 增加token以支持更复杂的查询构建
                temperature=0.2,  # 降低温度以确保查询的一致性和准确性
                system_prompt="You are a Google Search API query optimizer. Generate effective English search queries using appropriate search operators. Always output in English regardless of input language."
            )
            
            if optimized_query:
                # 清理结果
                optimized_query = optimized_query.strip()
                optimized_query = optimized_query.strip('"\'')
                optimized_query = optimized_query.strip()
                
                # 验证优化结果
                if len(optimized_query) > 0 and optimized_query.lower() not in ["optimized english query:", "optimized query:"]:
                    self.logger.info(f"查询优化成功: '{query}' → '{optimized_query}'")
                    return optimized_query
                else:
                    self.logger.warning(f"查询优化结果无效，使用原查询: {query}")
                    return query
            else:
                self.logger.warning(f"查询优化失败，使用原查询: {query}")
                return query
                
        except Exception as e:
            self.logger.error(f"查询优化失败: {e}")
            return query

    def _parse_comment_motivation_result(
        self,
        response: str,
        user_id: str,
        target_subreddit: str
    ) -> Dict[str, Any]:
        """解析评论动机分析结果"""
        result = {
            "user_id": user_id,
            "target_subreddit": target_subreddit,
            "professional_background": "",
            "participation_motivation": "",
            "interest_areas": "",
            "user_profile": "",
            "matching_value": "",
            "overall_assessment": "",
            "raw_analysis": response
        }
        
        try:
            lines = response.strip().split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                
                # 识别各个分析部分
                if line.startswith("专业背景："):
                    current_section = "professional_background"
                    result[current_section] = line.split("：", 1)[1] if "：" in line else ""
                elif line.startswith("参与动机："):
                    current_section = "participation_motivation"
                    result[current_section] = line.split("：", 1)[1] if "：" in line else ""
                elif line.startswith("兴趣领域："):
                    current_section = "interest_areas"
                    result[current_section] = line.split("：", 1)[1] if "：" in line else ""
                elif line.startswith("用户画像："):
                    current_section = "user_profile"
                    result[current_section] = line.split("：", 1)[1] if "：" in line else ""
                elif line.startswith("匹配价值："):
                    current_section = "matching_value"
                    result[current_section] = line.split("：", 1)[1] if "：" in line else ""
                elif line.startswith("总体评价："):
                    current_section = "overall_assessment"
                    result[current_section] = line.split("：", 1)[1] if "：" in line else ""
                elif current_section and line:
                    # 继续当前部分的内容
                    result[current_section] += " " + line
            
        except Exception as e:
            self.logger.error(f"解析评论动机分析结果失败: {e}")
            result["overall_assessment"] = f"解析失败: {str(e)}"
        
        return result


# 创建全局LLM服务实例
llm_service = LLMService()
