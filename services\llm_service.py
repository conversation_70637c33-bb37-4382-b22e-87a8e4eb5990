"""
CogBridges Search - LLM服务
实现Replicate API调用，支持参数和prompt管理
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Union
import replicate
from tenacity import retry, stop_after_attempt, wait_exponential

from config import config
from utils.logger_utils import get_logger, log_api_call



class LLMService:
    """LLM服务类，支持Replicate API调用"""
    
    def __init__(self):
        """初始化LLM服务"""
        self.logger = get_logger(__name__)
        
        # 检查配置
        self.configured = config.replicate_configured
        if not self.configured:
            self.logger.warning("Replicate API未配置，LLM服务不可用")
            return
        

        
        # 初始化Replicate客户端
        self.client = replicate.Client(api_token=config.REPLICATE_API_TOKEN)
        
        # 请求统计
        self.request_count = 0
        self.total_request_time = 0.0
        
        self.logger.info("LLM服务初始化成功")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @log_api_call("Replicate", "run", "POST")
    async def generate_text(
        self,
        prompt: str,
        model: str = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        system_prompt: str = None,
        **kwargs
    ) -> Optional[str]:
        """
        生成文本
        
        Args:
            prompt: 用户提示
            model: 模型名称，默认使用配置中的模型
            max_tokens: 最大token数
            temperature: 温度参数
            system_prompt: 系统提示
            **kwargs: 其他参数
            
        Returns:
            生成的文本
        """
        if not self.configured:
            raise ValueError("Replicate API未配置，无法使用LLM服务")
        
        start_time = time.time()
        model = model or config.REPLICATE_MODEL
        
        try:
            # 构建输入参数
            input_data = {
                "prompt": prompt,
                "max_tokens": max_tokens,
                "temperature": temperature,
                **kwargs
            }
            
            # 添加系统提示（如果支持）
            if system_prompt:
                input_data["system_prompt"] = system_prompt
            
            self.logger.debug(f"调用LLM模型: {model}, prompt长度: {len(prompt)}")
            
            # 调用Replicate API
            output = await asyncio.to_thread(
                self.client.run,
                model,
                input=input_data
            )
            
            # 处理输出
            if isinstance(output, list):
                result = "".join(str(item) for item in output)
            else:
                result = str(output)
            
            # 更新统计
            self.request_count += 1
            self.total_request_time += time.time() - start_time
            
            self.logger.info(f"LLM调用成功，输出长度: {len(result)}")
            return result
            
        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            raise
    

    
    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "configured": self.configured,
            "request_count": self.request_count,
            "total_request_time": self.total_request_time,
            "average_request_time": (
                self.total_request_time / self.request_count 
                if self.request_count > 0 else 0
            )
        }
    
    async def test_connection(self) -> bool:
        """测试Replicate API连接"""
        if not self.configured:
            return False

        try:
            # 简单的测试调用
            result = await self.generate_text(
                prompt="Hello",
                max_tokens=10,
                temperature=0.1
            )
            if result:
                self.logger.info("Replicate API连接测试成功")
                return True
            else:
                self.logger.error("Replicate API连接测试失败: 无响应")
                return False
        except Exception as e:
            self.logger.error(f"Replicate API连接测试失败: {e}")
            return False

    async def analyze_commenter_credibility(
        self,
        username: str,
        high_score_content: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        分析评论者可信度

        Args:
            username: 用户名
            high_score_content: 用户的高赞内容列表

        Returns:
            评论者可信度分析结果
        """
        if not high_score_content:
            self.logger.warning(f"用户{username}没有高赞内容可供分析")
            return {
                "username": username,
                "error": "没有足够的高赞内容进行分析"
            }

        # 记录分析开始
        self.logger.info(f"开始分析用户{username}的可信度 - 高赞内容数量: {len(high_score_content)}")

        try:
            # 构建分析提示
            prompt = self._build_credibility_analysis_prompt(username, high_score_content)

            # 调用LLM进行分析
            response = await self.generate_text(
                prompt=prompt,
                temperature=0.4,  # 适中的温度，平衡创造性和一致性
                max_tokens=1500
            )

            # 解析分析结果
            result = self._parse_credibility_analysis_result(response, username)

            # 记录分析完成
            self.logger.info(f"用户{username}可信度分析完成")

            return result

        except Exception as e:
            self.logger.error(f"用户{username}可信度分析失败: {e}")
            return {
                "username": username,
                "error": str(e)
            }





    def _build_credibility_analysis_prompt(
        self,
        username: str,
        high_score_content: List[Dict[str, Any]]
    ) -> str:
        """构建可信度分析提示"""

        # 构建用户内容列表
        content_list = []
        for i, content in enumerate(high_score_content[:20], 1):  # 限制最多20条
            content_type = content.get("type", "unknown")
            content_text = content.get("content", "")
            score = content.get("score", 0)
            subreddit = content.get("subreddit", "unknown")

            content_list.append(f"""
内容 {i} ({content_type}, 得分: {score}, 来自: {subreddit}):
{content_text[:500]}{"..." if len(content_text) > 500 else ""}
""")

        content_text = "\n".join(content_list)

        prompt = f"""You are analyzing the public Reddit comment history of a user.

Below are some this user's most upvoted comments, from various subreddits.

Your goal is to help us understand the *type of person* behind these comments by extracting 3 things:

1. Expertise — What areas does this person seem knowledgeable in?
2. Background Similarity — What do these comments reveal about the user's demographics, lifestyle, or personal context?
3. Worldview / Values / Stance — What values, perspectives, or recurring stances does this person seem to hold?

Please respond with a structured JSON object like this:

{{
  "expertise": {{
    "summary": "...",
    "evidence_comments": ["..."]
  }},
  "background_similarity": {{
    "summary": "...",
    "possible_tags": ["..."],
    "evidence_comments": ["..."]
  }},
  "worldview": {{
    "summary": "...",
    "value_tags": ["..."],
    "evidence_comments": ["..."]
  }}
}}

Make sure to:
- Use the exact phrasing or paraphrased version of user's comments in `evidence_comments`.
- Be concise and insightful in summaries.
- Avoid assumptions not grounded in the comments.

Here are the user's comments:
===
{content_text}
==="""

        return prompt

    def _parse_credibility_analysis_result(
        self,
        response: str,
        username: str
    ) -> Dict[str, Any]:
        """解析可信度分析结果"""
        try:
            import json
            import re

            # 清理响应文本
            response = response.strip()

            # 移除markdown代码块标记
            response = re.sub(r'```json\s*', '', response)
            response = re.sub(r'```\s*$', '', response)
            response = response.strip()

            # 尝试解析JSON
            try:
                result_dict = json.loads(response)
            except json.JSONDecodeError:
                self.logger.error(f"LLM响应不是有效的JSON格式: {response}")
                return {
                    "username": username,
                    "error": "JSON解析失败",
                    "raw_response": response
                }

            # 验证必需的字段
            required_fields = ["expertise", "background_similarity", "worldview"]
            for field in required_fields:
                if field not in result_dict:
                    result_dict[field] = {
                        "summary": "",
                        "evidence_comments": []
                    }

            # 添加用户名和活跃社区信息
            result_dict["username"] = username
            result_dict["profile_url"] = f"https://www.reddit.com/user/{username}"

            # 从分析结果中提取活跃社区（这里可以根据实际需要调整）
            result_dict["subreddits_active_in"] = []  # 这个字段需要从其他地方获取

            # 确保所有字段都有默认的evidence_comments
            for field in ["expertise", "background_similarity", "worldview"]:
                if field in result_dict and "evidence_comments" not in result_dict[field]:
                    result_dict[field]["evidence_comments"] = []

                # 确保background_similarity有possible_tags字段
                if field == "background_similarity" and "possible_tags" not in result_dict[field]:
                    result_dict[field]["possible_tags"] = []

                # 确保worldview有value_tags字段
                if field == "worldview" and "value_tags" not in result_dict[field]:
                    result_dict[field]["value_tags"] = []

            return result_dict

        except Exception as e:
            self.logger.error(f"解析可信度分析结果失败: {e}")
            return {
                "username": username,
                "error": str(e),
                "raw_response": response
            }







    async def optimize_query_for_google_search(self, query: str) -> str:
        """
        优化搜索查询以更好地适配Google Search API，支持多语言输入，确保英文输出
        
        Args:
            query: 原始查询字符串（支持多语言）
            
        Returns:
            优化后的英文查询字符串
        """
        if not self.configured:
            self.logger.warning("LLM服务未配置，跳过查询优化")
            return query
        
        try:
            # 构建查询优化提示
            optimization_prompt = f"""
You are an AI assistant tasked with optimizing queries for the Google Search API. Your goal is to generate an effective search query based on the user's search intent, using Google search operators to retrieve the most accurate and relevant results. **Regardless of the input language, the generated search query must always be in English** to ensure compatibility with the Google Search API and broad applicability of results.

Original Query: {query}

When constructing the search query, consider using the following search operators when applicable:
- **Quotes (" ")**: Search for an exact phrase. For example, `"machine learning"` finds pages containing the exact phrase.
- **Minus (-)**: Exclude specific terms. For example, `jaguar -car` finds pages about the animal, excluding car-related content.
- **OR**: Search for pages containing either term. For example, `vacation OR trip`.
- **site:**: Restrict search to a specific website. For example, `site:reddit.com` searches only reddit.com.
- **filetype:**: Find specific file types. For example, `filetype:pdf` finds PDF files.
- **intitle:**: Search for pages with a specific word in the title. For example, `intitle:guide`.
- **inurl:**: Search for pages with a specific word in the URL. For example, `inurl:blog`.

Ensure the search query is clear, specific, and meets the user's needs. If the user provides input in a non-English language, translate their intent into English and generate the corresponding English query. For example, a Chinese input like "人工智能新闻" should yield `"artificial intelligence" news`.

Here are some example guidelines:
1. **User Intent**: Find recent news articles about artificial intelligence (input language: any, e.g., Chinese "人工智能新闻").
   **Optimized Query**: `"artificial intelligence" news`
2. **User Intent**: Search for Python programming tutorials, excluding videos (input language: any, e.g., Spanish "tutoriales de programación en Python sin videos").
   **Optimized Query**: `python programming tutorial -video`
3. **User Intent**: Find PDF documents on climate change from government websites (input language: any, e.g., French "changement climatique documents PDF gouvernement").
   **Optimized Query**: `climate change site:gov filetype:pdf`
4. **User Intent**: Find pages mentioning "blockchain" and "cryptocurrency" but not "bitcoin" (input language: any, e.g., Japanese "ブロックチェーン 暗号通貨 ビットコインなし").
   **Optimized Query**: `blockchain AND cryptocurrency -bitcoin`

Use logical operators like AND, OR, NOT appropriately to combine terms. The generated query must be in English, concise, and compliant with Google Search API syntax.

Your goal is to generate an English search query that, when used with the Google Search API, returns the most relevant and accurate results.

Optimized English Query:
"""
            
            # 调用LLM进行优化
            optimized_query = await self.generate_text(
                prompt=optimization_prompt,
                max_tokens=200,  # 增加token以支持更复杂的查询构建
                temperature=0.2,  # 降低温度以确保查询的一致性和准确性
                system_prompt="You are a Google Search API query optimizer. Generate effective English search queries using appropriate search operators. Always output in English regardless of input language."
            )
            
            if optimized_query:
                # 清理结果
                optimized_query = optimized_query.strip()
                optimized_query = optimized_query.strip('"\'')
                optimized_query = optimized_query.strip()
                
                # 验证优化结果
                if len(optimized_query) > 0 and optimized_query.lower() not in ["optimized english query:", "optimized query:"]:
                    self.logger.info(f"查询优化成功: '{query}' → '{optimized_query}'")
                    return optimized_query
                else:
                    self.logger.warning(f"查询优化结果无效，使用原查询: {query}")
                    return query
            else:
                self.logger.warning(f"查询优化失败，使用原查询: {query}")
                return query
                
        except Exception as e:
            self.logger.error(f"查询优化失败: {e}")
            return query




# 创建全局LLM服务实例
llm_service = LLMService()
