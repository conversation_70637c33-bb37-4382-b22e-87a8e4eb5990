import React from 'react'
import { User, ExternalLink, Tag, Brain, Globe, Award } from 'lucide-react'

const CommenterCredibilityCard = ({ credibilityData, className = "" }) => {
  if (!credibilityData) return null

  const {
    username,
    profile_url,
    subreddits_active_in = [],
    expertise = {},
    background_similarity = {},
    worldview = {}
  } = credibilityData

  return (
    <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 mt-4 ${className}`}>
      {/* 用户信息头部 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <User className="w-5 h-5 text-gray-600" />
          <span className="font-medium text-gray-800">@{username}</span>
          {profile_url && (
            <a 
              href={profile_url} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-500 hover:text-blue-700"
            >
              <ExternalLink className="w-4 h-4" />
            </a>
          )}
        </div>
      </div>

      {/* 活跃社区 */}
      {subreddits_active_in && subreddits_active_in.length > 0 && (
        <div className="mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <Tag className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">活跃社区</span>
          </div>
          <div className="flex flex-wrap gap-1">
            {subreddits_active_in.slice(0, 5).map((subreddit, index) => (
              <span 
                key={index}
                className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded"
              >
                {subreddit}
              </span>
            ))}
            {subreddits_active_in.length > 5 && (
              <span className="text-xs text-gray-500">
                +{subreddits_active_in.length - 5} 更多
              </span>
            )}
          </div>
        </div>
      )}

      {/* 专业性 */}
      {expertise.summary && (
        <div className="mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <Award className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium text-gray-700">专业性（Expertise）</span>
          </div>
          <p className="text-sm text-gray-600 mb-2">{expertise.summary}</p>
          {expertise.evidence_comments && expertise.evidence_comments.length > 0 && (
            <div className="text-xs text-gray-500">
              <span className="font-medium">证据：</span>
              <span className="italic">"{expertise.evidence_comments[0]?.substring(0, 100)}..."</span>
            </div>
          )}
        </div>
      )}

      {/* 个人背景 */}
      {background_similarity.summary && (
        <div className="mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <User className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-700">个人背景（Background Similarity）</span>
          </div>
          <p className="text-sm text-gray-600 mb-2">{background_similarity.summary}</p>
          
          {/* 背景标签 */}
          {background_similarity.possible_tags && background_similarity.possible_tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {background_similarity.possible_tags.slice(0, 3).map((tag, index) => (
                <span 
                  key={index}
                  className="text-xs bg-blue-50 text-blue-600 px-2 py-1 rounded border border-blue-200"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}
          
          {background_similarity.evidence_comments && background_similarity.evidence_comments.length > 0 && (
            <div className="text-xs text-gray-500">
              <span className="font-medium">证据：</span>
              <span className="italic">"{background_similarity.evidence_comments[0]?.substring(0, 100)}..."</span>
            </div>
          )}
        </div>
      )}

      {/* 世界观 / 价值观 */}
      {worldview.summary && (
        <div className="mb-0">
          <div className="flex items-center space-x-2 mb-2">
            <Globe className="w-4 h-4 text-purple-600" />
            <span className="text-sm font-medium text-gray-700">世界观 / 价值观（Worldview）</span>
          </div>
          <p className="text-sm text-gray-600 mb-2">{worldview.summary}</p>
          
          {/* 价值观标签 */}
          {worldview.value_tags && worldview.value_tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {worldview.value_tags.slice(0, 3).map((tag, index) => (
                <span 
                  key={index}
                  className="text-xs bg-purple-50 text-purple-600 px-2 py-1 rounded border border-purple-200"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}
          
          {worldview.evidence_comments && worldview.evidence_comments.length > 0 && (
            <div className="text-xs text-gray-500">
              <span className="font-medium">证据：</span>
              <span className="italic">"{worldview.evidence_comments[0]?.substring(0, 100)}..."</span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default CommenterCredibilityCard
