import os
import requests
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 检查API密钥是否存在
api_key = os.getenv('XAI_API_KEY')
if not api_key:
    print("❌ 错误：未找到XAI_API_KEY环境变量")
    print("请确保在.env文件中设置了正确的XAI_API_KEY")
    exit(1)

url = "https://api.x.ai/v1/chat/completions"
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {api_key}"
}
payload = {
    "messages": [
        {
            "role": "user",
            "content": '''{父母从来不提供情绪价值，经常想要逃离}
使用英文在reddit内搜索{query}，并返回相关的帖子，然后取其中相关的顶级评论。总共取15条最相关的顶级评论即可

要求：
优先选择发布时间较近的帖子
附上redditor昵称和其主页url
附上帖子title,selftext和其url
每条评论要完整保留、直接呈现，严禁做任何修改
''' #{query}为用户输入的查询
        }
    ],
    "search_parameters": {
        "mode": "on",
        "max_search_results": 29,
        "sources": [
          { "type": "web", "allowed_websites": ["reddit.com"] },
        ]
    },
    "model": "grok-3"
}

try:
    print("🚀 正在调用X.AI API...")
    start_time = time.time()
    
    response = requests.post(url, headers=headers, json=payload)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    if response.status_code == 200:
        result = response.json()
        print("✅ API调用成功！")
        print(f"⏱️ 总耗时：{elapsed_time:.2f}秒")
        print(result)
    else:
        print(f"❌ API调用失败，状态码：{response.status_code}")
        print(f"错误信息：{response.text}")
        print(f"⏱️ 耗时：{elapsed_time:.2f}秒")
        
except requests.exceptions.RequestException as e:
    print(f"❌ 网络请求错误：{e}")
except Exception as e:
    print(f"❌ 其他错误：{e}")