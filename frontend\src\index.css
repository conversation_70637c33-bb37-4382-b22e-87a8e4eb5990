/* 导入Google Fonts的Inter字体，支持多种字重 */
/* 这个字体是现代化的无衬线字体，适合Web应用 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Tailwind CSS的基础样式导入 */
/* @tailwind base: 导入Tailwind的基础样式，重置浏览器默认样式 */
@tailwind base;
/* @tailwind components: 导入Tailwind的组件样式 */
@tailwind components;
/* @tailwind utilities: 导入Tailwind的工具类样式 */
@tailwind utilities;

/* 自定义基础样式层 */
@layer base {
  /* 为html和body元素设置字体 */
  html, body {
    /* 设置字体族，优先使用Inter，然后是系统字体，最后是无衬线字体 */
    font-family: 'Inter', system-ui, sans-serif;
  }
}

/* 自定义工具类 */
@layer utilities {
  /* 文本截断样式 */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 自定义脉冲动画 */
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  /* 自定义呼吸动画 */
  .animate-breathe {
    animation: breathe 4s ease-in-out infinite;
  }
  
  /* 自定义闪烁动画 */
  .animate-twinkle {
    animation: twinkle 2s ease-in-out infinite;
  }
  
  /* 自定义进度条动画 */
  .animate-progress {
    animation: progress 2s ease-out infinite;
  }
  
  /* 自定义浮动动画 */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  /* 自定义旋转动画 */
  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }
}

/* 自定义动画关键帧 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 渐变背景动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 加载状态的特殊样式 */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
