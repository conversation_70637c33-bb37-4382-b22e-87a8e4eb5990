"""
CogBridges Search - 数据库模型
使用SQLAlchemy定义数据库表结构，对应现有的JSON数据结构
"""

from sqlalchemy import Column, String, Integer, Float, Text, DateTime, Boolean, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, Any, Optional

Base = declarative_base()


class SearchSession(Base):
    """搜索会话表 - 对应完整会话数据"""
    __tablename__ = 'search_sessions'
    
    id = Column(String(50), primary_key=True)  # session_id
    query = Column(Text, nullable=False)  # 搜索查询
    timestamp = Column(DateTime, default=func.now())  # 创建时间
    search_type = Column(String(20), default='reddit')  # 搜索类型
    max_results = Column(Integer, default=5)  # 最大结果数
    site_filter = Column(String(100), default='site:reddit.com')  # 站点过滤
    
    # 搜索结果统计
    google_results_count = Column(Integer, default=0)
    reddit_posts_count = Column(Integer, default=0)
    reddit_comments_count = Column(Integer, default=0)
    
    # 状态信息
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    
    # LLM分析结果
    llm_analysis_success = Column(Boolean, default=False)
    llm_analysis_time = Column(Float, default=0.0)
    llm_analysis_error = Column(Text, nullable=True)

    # JSON备份数据
    raw_data = Column(JSON, nullable=True)  # 完整的原始数据

    # 关联关系
    google_results = relationship("GoogleSearchResult", back_populates="session", cascade="all, delete-orphan")
    reddit_posts = relationship("RedditPost", back_populates="session", cascade="all, delete-orphan")
    reddit_comments = relationship("RedditComment", back_populates="session", cascade="all, delete-orphan")
    user_histories = relationship("UserHistory", back_populates="session", cascade="all, delete-orphan")
    subreddit_similarities = relationship("SubredditSimilarity", back_populates="session", cascade="all, delete-orphan")

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class CommenterCredibility(Base):
    """评论者可信度表"""
    __tablename__ = 'commenter_credibility'

    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(100), nullable=False, unique=True)
    profile_url = Column(Text, nullable=True)

    # 活跃社区
    subreddits_active_in = Column(JSON, nullable=True)  # List[str]

    # 专业性信息
    expertise_summary = Column(Text, nullable=True)
    expertise_evidence = Column(JSON, nullable=True)  # List[str]

    # 背景相似性信息
    background_summary = Column(Text, nullable=True)
    background_tags = Column(JSON, nullable=True)  # List[str]
    background_evidence = Column(JSON, nullable=True)  # List[str]

    # 世界观信息
    worldview_summary = Column(Text, nullable=True)
    worldview_tags = Column(JSON, nullable=True)  # List[str]
    worldview_evidence = Column(JSON, nullable=True)  # List[str]

    # 分析元数据
    analysis_success = Column(Boolean, default=False)
    error_message = Column(Text, nullable=True)
    processing_time = Column(Float, nullable=True)
    raw_llm_response = Column(Text, nullable=True)

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "query": self.query,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "search_type": self.search_type,
            "max_results": self.max_results,
            "site_filter": self.site_filter,
            "google_results_count": self.google_results_count,
            "reddit_posts_count": self.reddit_posts_count,
            "reddit_comments_count": self.reddit_comments_count,
            "success": self.success,
            "error_message": self.error_message,
            "llm_analysis_success": self.llm_analysis_success,
            "llm_analysis_time": self.llm_analysis_time,
            "llm_analysis_error": self.llm_analysis_error,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class GoogleSearchResult(Base):
    """Google搜索结果表"""
    __tablename__ = 'google_search_results'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False)
    
    title = Column(Text, nullable=False)
    url = Column(Text, nullable=False)
    snippet = Column(Text, nullable=True)
    display_url = Column(Text, nullable=True)
    rank = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    session = relationship("SearchSession", back_populates="google_results")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "title": self.title,
            "url": self.url,
            "snippet": self.snippet,
            "display_url": self.display_url,
            "rank": self.rank,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class RedditPost(Base):
    """Reddit帖子表"""
    __tablename__ = 'reddit_posts'
    
    id = Column(String(20), primary_key=True)  # Reddit post ID
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False)
    
    title = Column(Text, nullable=False)
    selftext = Column(Text, nullable=True)
    author = Column(String(100), nullable=True)
    score = Column(Integer, default=0)
    num_comments = Column(Integer, default=0)
    created_utc = Column(Float, nullable=False)
    subreddit = Column(String(100), nullable=True)
    permalink = Column(Text, nullable=True)
    url = Column(Text, nullable=True)
    
    # 分析结果
    motivation_analysis = Column(JSON, nullable=True)
    
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    session = relationship("SearchSession", back_populates="reddit_posts")
    comments = relationship("RedditComment", back_populates="post", cascade="all, delete-orphan")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "title": self.title,
            "selftext": self.selftext,
            "author": self.author,
            "score": self.score,
            "num_comments": self.num_comments,
            "created_utc": self.created_utc,
            "subreddit": self.subreddit,
            "permalink": self.permalink,
            "url": self.url,
            "motivation_analysis": self.motivation_analysis,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class RedditComment(Base):
    """Reddit评论表"""
    __tablename__ = 'reddit_comments'
    
    id = Column(String(20), primary_key=True)  # Reddit comment ID
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False)
    post_id = Column(String(20), ForeignKey('reddit_posts.id'), nullable=True)
    
    body = Column(Text, nullable=False)
    author = Column(String(100), nullable=True)
    score = Column(Integer, default=0)
    created_utc = Column(Float, nullable=False)
    parent_id = Column(String(20), nullable=True)
    subreddit = Column(String(100), nullable=True)
    permalink = Column(Text, nullable=True)
    
    # 分析结果
    credibility_analysis = Column(JSON, nullable=True)

    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    session = relationship("SearchSession", back_populates="reddit_comments")
    post = relationship("RedditPost", back_populates="comments")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "post_id": self.post_id,
            "body": self.body,
            "author": self.author,
            "score": self.score,
            "created_utc": self.created_utc,
            "parent_id": self.parent_id,
            "subreddit": self.subreddit,
            "permalink": self.permalink,
            "motivation_analysis": self.motivation_analysis,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class UserHistory(Base):
    """用户历史数据表"""
    __tablename__ = 'user_histories'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False)
    username = Column(String(100), nullable=False)
    
    # 用户统计信息
    total_comments = Column(Integer, default=0)
    total_posts = Column(Integer, default=0)
    account_created_utc = Column(Float, nullable=True)
    
    # 历史数据（JSON格式存储）
    comments_data = Column(JSON, nullable=True)
    posts_data = Column(JSON, nullable=True)
    
    created_at = Column(DateTime, default=func.now())

    # 关联关系
    session = relationship("SearchSession", back_populates="user_histories")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "username": self.username,
            "total_comments": self.total_comments,
            "total_posts": self.total_posts,
            "account_created_utc": self.account_created_utc,
            "comments_data": self.comments_data,
            "posts_data": self.posts_data,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class SubredditSimilarity(Base):
    """子版块相似性分析表 - 存储LLM筛选子版块的结果"""
    __tablename__ = 'subreddit_similarities'

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False)
    username = Column(String(100), nullable=False)

    # 目标子版块信息
    target_subreddits = Column(JSON, nullable=False)  # 目标子版块列表
    user_subreddits = Column(JSON, nullable=False)    # 用户关注的子版块列表

    # 相似性分析结果
    similarity_results = Column(JSON, nullable=False)  # 完整的相似性分析结果
    all_similar_subreddits = Column(JSON, nullable=True)  # 所有相似的子版块

    # 分析元数据
    analysis_success = Column(Boolean, default=True)
    analysis_error = Column(Text, nullable=True)
    llm_response_raw = Column(Text, nullable=True)  # LLM原始响应

    created_at = Column(DateTime, default=func.now())

    # 关联关系
    session = relationship("SearchSession", back_populates="subreddit_similarities")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "username": self.username,
            "target_subreddits": self.target_subreddits,
            "user_subreddits": self.user_subreddits,
            "similarity_results": self.similarity_results,
            "all_similar_subreddits": self.all_similar_subreddits,
            "analysis_success": self.analysis_success,
            "analysis_error": self.analysis_error,
            "llm_response_raw": self.llm_response_raw,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class CommentMotivationAnalysis(Base):
    """评论动机分析表 - 存储LLM分析用户画像的结果"""
    __tablename__ = 'comment_motivation_analyses'

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False)
    username = Column(String(100), nullable=False)

    # 关联的评论信息
    comment_id = Column(String(20), ForeignKey('reddit_comments.id'), nullable=True)
    target_subreddit = Column(String(100), nullable=False)

    # 分析结果
    professional_background = Column(Text, nullable=True)    # 专业背景分析
    participation_motivation = Column(Text, nullable=True)   # 参与动机分析
    interest_areas = Column(Text, nullable=True)             # 兴趣领域分析
    user_profile = Column(Text, nullable=True)               # 用户画像推断
    matching_value = Column(Text, nullable=True)             # 潜在匹配价值
    overall_assessment = Column(Text, nullable=True)         # 总体评价

    # 分析元数据
    analysis_success = Column(Boolean, default=True)
    analysis_error = Column(Text, nullable=True)
    llm_response_raw = Column(Text, nullable=True)  # LLM原始响应

    # 输入数据（用于复现分析）
    target_post_data = Column(JSON, nullable=True)           # 目标帖子数据
    user_comment_data = Column(JSON, nullable=True)          # 用户评论数据
    similar_subreddits_data = Column(JSON, nullable=True)    # 相似子版块数据
    user_overview_data = Column(JSON, nullable=True)         # 用户概览数据

    created_at = Column(DateTime, default=func.now())

    # 关联关系
    session = relationship("SearchSession")
    comment = relationship("RedditComment")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "username": self.username,
            "comment_id": self.comment_id,
            "target_subreddit": self.target_subreddit,
            "professional_background": self.professional_background,
            "participation_motivation": self.participation_motivation,
            "interest_areas": self.interest_areas,
            "user_profile": self.user_profile,
            "matching_value": self.matching_value,
            "overall_assessment": self.overall_assessment,
            "analysis_success": self.analysis_success,
            "analysis_error": self.analysis_error,
            "llm_response_raw": self.llm_response_raw,
            "target_post_data": self.target_post_data,
            "user_comment_data": self.user_comment_data,
            "similar_subreddits_data": self.similar_subreddits_data,
            "user_overview_data": self.user_overview_data,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
