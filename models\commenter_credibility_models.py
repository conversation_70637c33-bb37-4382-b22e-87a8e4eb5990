"""
CogBridges Search - 评论者可信度数据模型
定义评论者可信度卡片的数据结构和相关模型
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from datetime import datetime


@dataclass
class ExpertiseInfo:
    """专业性信息"""
    summary: str
    evidence_comments: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "summary": self.summary,
            "evidence_comments": self.evidence_comments
        }


@dataclass
class BackgroundSimilarityInfo:
    """背景相似性信息"""
    summary: str
    possible_tags: List[str]
    evidence_comments: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "summary": self.summary,
            "possible_tags": self.possible_tags,
            "evidence_comments": self.evidence_comments
        }


@dataclass
class WorldviewInfo:
    """世界观信息"""
    summary: str
    value_tags: List[str]
    evidence_comments: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "summary": self.summary,
            "value_tags": self.value_tags,
            "evidence_comments": self.evidence_comments
        }


@dataclass
class CommenterCredibilityCard:
    """评论者可信度卡片"""
    username: str
    profile_url: str
    subreddits_active_in: List[str]
    expertise: ExpertiseInfo
    background_similarity: BackgroundSimilarityInfo
    worldview: WorldviewInfo
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "username": self.username,
            "profile_url": self.profile_url,
            "subreddits_active_in": self.subreddits_active_in,
            "expertise": self.expertise.to_dict(),
            "background_similarity": self.background_similarity.to_dict(),
            "worldview": self.worldview.to_dict(),
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CommenterCredibilityCard':
        """从字典创建实例"""
        expertise = ExpertiseInfo(
            summary=data.get("expertise", {}).get("summary", ""),
            evidence_comments=data.get("expertise", {}).get("evidence_comments", [])
        )
        
        background_similarity = BackgroundSimilarityInfo(
            summary=data.get("background_similarity", {}).get("summary", ""),
            possible_tags=data.get("background_similarity", {}).get("possible_tags", []),
            evidence_comments=data.get("background_similarity", {}).get("evidence_comments", [])
        )
        
        worldview = WorldviewInfo(
            summary=data.get("worldview", {}).get("summary", ""),
            value_tags=data.get("worldview", {}).get("value_tags", []),
            evidence_comments=data.get("worldview", {}).get("evidence_comments", [])
        )
        
        created_at = None
        if data.get("created_at"):
            created_at = datetime.fromisoformat(data["created_at"])
        
        return cls(
            username=data.get("username", ""),
            profile_url=data.get("profile_url", ""),
            subreddits_active_in=data.get("subreddits_active_in", []),
            expertise=expertise,
            background_similarity=background_similarity,
            worldview=worldview,
            created_at=created_at
        )


@dataclass
class CommenterCredibilityAnalysisResult:
    """评论者可信度分析结果"""
    username: str
    success: bool
    credibility_card: Optional[CommenterCredibilityCard] = None
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    raw_llm_response: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "username": self.username,
            "success": self.success,
            "error_message": self.error_message,
            "processing_time": self.processing_time,
            "raw_llm_response": self.raw_llm_response
        }
        
        if self.credibility_card:
            result["credibility_card"] = self.credibility_card.to_dict()
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CommenterCredibilityAnalysisResult':
        """从字典创建实例"""
        credibility_card = None
        if data.get("credibility_card"):
            credibility_card = CommenterCredibilityCard.from_dict(data["credibility_card"])
        
        return cls(
            username=data.get("username", ""),
            success=data.get("success", False),
            credibility_card=credibility_card,
            error_message=data.get("error_message"),
            processing_time=data.get("processing_time"),
            raw_llm_response=data.get("raw_llm_response")
        )


# JSON Schema 定义（用于验证和文档）
COMMENTER_CREDIBILITY_CARD_SCHEMA = {
    "type": "object",
    "properties": {
        "username": {"type": "string"},
        "profile_url": {"type": "string"},
        "subreddits_active_in": {
            "type": "array",
            "items": {"type": "string"}
        },
        "expertise": {
            "type": "object",
            "properties": {
                "summary": {"type": "string"},
                "evidence_comments": {
                    "type": "array",
                    "items": {"type": "string"}
                }
            },
            "required": ["summary", "evidence_comments"]
        },
        "background_similarity": {
            "type": "object",
            "properties": {
                "summary": {"type": "string"},
                "possible_tags": {
                    "type": "array",
                    "items": {"type": "string"}
                },
                "evidence_comments": {
                    "type": "array",
                    "items": {"type": "string"}
                }
            },
            "required": ["summary", "possible_tags", "evidence_comments"]
        },
        "worldview": {
            "type": "object",
            "properties": {
                "summary": {"type": "string"},
                "value_tags": {
                    "type": "array",
                    "items": {"type": "string"}
                },
                "evidence_comments": {
                    "type": "array",
                    "items": {"type": "string"}
                }
            },
            "required": ["summary", "value_tags", "evidence_comments"]
        }
    },
    "required": ["username", "profile_url", "subreddits_active_in", "expertise", "background_similarity", "worldview"]
}
