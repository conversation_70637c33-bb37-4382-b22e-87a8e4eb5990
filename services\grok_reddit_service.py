"""
CogBridges Search - Grok Reddit搜索服务
使用X.AI Grok API进行Reddit搜索和数据获取，支持结构化输出
"""

import os
import time
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from dotenv import load_dotenv


from config import config
from utils.logger_utils import get_logger

# 加载环境变量
load_dotenv()


class RedditComment(BaseModel):
    """Reddit评论结构"""
    author: str = Field(description="评论作者用户名")
    author_url: str = Field(description="评论作者主页URL")
    body: str = Field(description="评论完整内容")
    score: int = Field(description="评论得分", default=0)
    created_utc: Optional[str] = Field(description="评论创建时间", default=None)
    permalink: Optional[str] = Field(description="评论永久链接", default=None)


class RedditPost(BaseModel):
    """Reddit帖子结构"""
    title: str = Field(description="帖子标题")
    selftext: str = Field(description="帖子正文内容")
    url: str = Field(description="帖子URL")
    author: str = Field(description="帖子作者用户名")
    subreddit: str = Field(description="所属子版块")
    score: int = Field(description="帖子得分", default=0)
    created_utc: Optional[str] = Field(description="帖子创建时间", default=None)
    num_comments: int = Field(description="评论数量", default=0)


class RedditSearchResult(BaseModel):
    """Reddit搜索结果结构"""
    posts: List[RedditPost] = Field(description="相关帖子列表")
    comments: List[RedditComment] = Field(description="相关顶级评论列表")
    total_posts_found: int = Field(description="找到的帖子总数", default=0)
    total_comments_found: int = Field(description="找到的评论总数", default=0)
    search_query: str = Field(description="搜索查询")
    search_time: float = Field(description="搜索耗时（秒）", default=0.0)


class GrokRedditService:
    """Grok Reddit搜索服务"""
    
    def __init__(self):
        """初始化Grok Reddit服务"""
        self.logger = get_logger(__name__)
        
        # 检查API密钥
        self.api_key = os.getenv('XAI_API_KEY')
        if not self.api_key:
            self.logger.error("未找到XAI_API_KEY环境变量")
            self.configured = False
        else:
            self.configured = True
            self.logger.info("Grok Reddit服务初始化成功")
        
        # API配置
        self.api_url = "https://api.x.ai/v1/chat/completions"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 统计信息
        self.request_count = 0
        self.total_request_time = 0.0
    
    async def search_reddit(self, query: str, max_comments: int = 15) -> Dict[str, Any]:
        """
        使用Grok API搜索Reddit内容
        
        Args:
            query: 搜索查询
            max_comments: 最大评论数量
            
        Returns:
            搜索结果字典
        """
        if not self.configured:
            return {
                "success": False,
                "error": "Grok API未配置",
                "results": None,
                "search_time": 0.0
            }
        
        self.logger.info(f"开始Grok Reddit搜索: {query}")
        start_time = time.time()
        
        try:
            # 构建搜索提示词
            search_prompt = self._build_search_prompt(query, max_comments)
            
            # 构建API请求 - 使用OpenAI SDK格式进行结构化输出
            from openai import OpenAI

            # 初始化客户端
            client = OpenAI(
                api_key=self.api_key,
                base_url="https://api.x.ai/v1"
            )

            # 使用结构化输出
            completion = client.beta.chat.completions.parse(
                model="grok-3",
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个专业的Reddit搜索分析师。请根据用户查询搜索Reddit内容，并按照指定的结构化格式返回结果。"
                    },
                    {
                        "role": "user",
                        "content": search_prompt
                    }
                ],
                response_format=RedditSearchResult
            )
            
            # 获取结构化输出结果
            parsed_result = completion.choices[0].message.parsed

            search_time = time.time() - start_time
            self.request_count += 1
            self.total_request_time += search_time

            if parsed_result:
                self.logger.info(f"Grok Reddit搜索成功，耗时: {search_time:.2f}秒")
                self.logger.info(f"找到 {len(parsed_result.posts)} 个帖子，{len(parsed_result.comments)} 条评论")

                return {
                    "success": True,
                    "results": parsed_result,
                    "search_time": search_time,
                    "error": None
                }
            else:
                error_msg = "Grok API返回空结果"
                self.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "results": None,
                    "search_time": search_time
                }
                
        except Exception as timeout_error:
            if "timeout" in str(timeout_error).lower():
                error_msg = "Grok API请求超时"
            else:
                error_msg = f"Grok API调用异常: {str(timeout_error)}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "results": None,
                "search_time": time.time() - start_time
            }

    
    def _build_search_prompt(self, query: str, max_comments: int) -> str:
        """构建搜索提示词"""
        return f"""使用英文在reddit内搜索"{query}"，并返回相关的帖子和顶级评论。

要求：
1. 优先选择发布时间较近的帖子（最近1年内）
2. 总共取{max_comments}条最相关的顶级评论
3. 每个帖子包含：标题、正文内容、URL、作者、子版块、得分、评论数
4. 每条评论包含：作者昵称、作者主页URL、完整评论内容、得分、时间
5. 每条评论要完整保留、直接呈现，严禁做任何修改
6. 确保返回的数据结构完整且格式正确
7. 优先返回未被删除的帖子和评论，如果帖子或评论被删除，则选择其他帖子和评论

注意：
- 作者主页URL格式：https://reddit.com/user/[用户名]
- 帖子URL应该是完整的reddit链接
- 评论内容必须完整，不能截断或修改
- 优先选择有实质内容的评论，避免简单的"同意"或"谢谢"类评论
"""
    

    
    def get_statistics(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "api_configured": self.configured,
            "service_type": "grok_reddit_service",
            "request_count": self.request_count,
            "total_request_time": self.total_request_time,
            "average_request_time": self.total_request_time / max(self.request_count, 1)
        }
    
    async def close(self):
        """关闭服务"""
        self.logger.info("Grok Reddit服务已关闭")
