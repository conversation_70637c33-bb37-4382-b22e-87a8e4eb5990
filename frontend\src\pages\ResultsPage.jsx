import { useState, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import ResultCard from '../components/ResultCard'
import SearchInput from '../components/SearchInput'
import { ArrowLeft, RefreshCw, AlertCircle } from 'lucide-react'
import { api } from '../services/api'

const ResultsPage = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [results, setResults] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const query = location.state?.query || ''
  const searchResult = location.state?.searchResult
  const searchTime = location.state?.searchTime || 0

  // 转换后端数据为前端展示格式
  const convertBackendDataToResults = (backendData) => {
    if (!backendData || !backendData.reddit_posts) {
      return []
    }

    const convertedResults = []
    
    // 处理Reddit帖子和评论数据
    backendData.reddit_posts.forEach((postData, postIndex) => {
      const post = postData.post || postData
      const comments = postData.comments || []
      
      // 为每个有效评论创建一个结果项
      comments.forEach((comment, commentIndex) => {
        if (comment.author && comment.body && comment.author !== '[deleted]') {
          const resultId = `${postIndex}-${commentIndex}`
          
          // 获取评论者历史数据
          const commenterHistory = backendData.commenters_history?.[comment.author] || {}
          
          // 获取评论者可信度数据
          const credibilityAnalysis = backendData?.llm_analysis?.credibility_analysis?.[comment.author] || null

          convertedResults.push({
            id: resultId,
            author: comment.author,
            subreddit: post.subreddit || 'unknown',
            comment: comment.body, // 保存完整评论内容
            fullComment: comment.body, // 完整评论字段
            score: comment.score || 0,
            karma: commenterHistory.comment_karma || commenterHistory.karma || 0,
            url: `https://reddit.com${post.permalink || ''}`,
            // 保存原始数据用于详情页
            _rawData: {
              post,
              comment,
              commenterHistory,
              backendData,
              credibilityAnalysis
            }
          })
        }
      })
    })

    return convertedResults
  }





  // 加载和处理数据
  useEffect(() => {
    if (!query) {
      navigate('/')
      return
    }

    if (!searchResult) {
      setError('没有搜索结果数据')
      setLoading(false)
      return
    }

    try {
      console.log('处理搜索结果:', searchResult)
      
      if (!searchResult.success) {
        setError(searchResult.error_message || '搜索失败')
        setLoading(false)
        return
      }

      // 转换数据格式
      const convertedResults = convertBackendDataToResults(searchResult)
      console.log('转换后的结果:', convertedResults)
      
      // 尝试保存到本地存储，供DetailPage使用
      try {
        // 创建一个简化版本的结果，移除大型数据字段
        const simplifiedResults = convertedResults.map(result => ({
          ...result,
          // 移除可能很大的原始数据
          _rawData: {
            post: { id: result._rawData?.post?.id },
            comment: { id: result._rawData?.comment?.id },
            // 保留必要的用户历史数据的简化版本
            commenterHistory: {
              comment_karma: result._rawData?.commenterHistory?.comment_karma,
              link_karma: result._rawData?.commenterHistory?.link_karma
            }
          }
        }))
        
        localStorage.setItem('cogbridges_results', JSON.stringify(simplifiedResults))
      } catch (storageError) {
        console.warn('无法保存到本地存储（数据过大）:', storageError)
        // 如果存储失败，清除旧数据并尝试只保存基本信息
        try {
          localStorage.removeItem('cogbridges_results')
          const basicResults = convertedResults.map(result => ({
            id: result.id,
            author: result.author,
            subreddit: result.subreddit,
            comment: result.comment,
            summary: result.summary,
            score: result.score,
            karma: result.karma,
            insights: result.insights,
            recommendation: result.recommendation,
            url: result.url,
            tags: result.tags
          }))
          localStorage.setItem('cogbridges_results', JSON.stringify(basicResults))
        } catch (fallbackError) {
          console.error('即使简化版本也无法存储:', fallbackError)
          // 完全跳过本地存储
        }
      }
      
      setResults(convertedResults)
      setError(null)
    } catch (err) {
      console.error('数据处理失败:', err)
      setError('数据处理失败: ' + err.message)
    } finally {
      setLoading(false)
    }
  }, [query, searchResult, navigate])

  const handleNewSearch = async (newQuery) => {
    try {
      setLoading(true)

      // 直接导航到LoadingPage，让LoadingPage执行搜索
      navigate('/loading', {
        state: {
          query: newQuery,
          sessionId: Date.now().toString(),
          timestamp: Date.now()
        }
      })
    } catch (error) {
      alert(`搜索失败: ${error.message}`)
      setLoading(false)
    }
  }

  const handleViewDetail = (result) => {
    navigate(`/detail/${result.id}`, { 
      state: { 
        result,
        query
      } 
    })
  }

  const handleBookmark = async (result, isBookmarked) => {
    try {
      if (isBookmarked) {
        await api.addBookmark({
          id: result.id,
          query,
          result
        })
        console.log('添加收藏成功')
      } else {
        await api.removeBookmark(result.id)
        console.log('取消收藏成功')
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
    }
  }



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-primary-500 mx-auto mb-4" />
          <p className="text-gray-600">正在处理搜索结果...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => navigate('/')}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部搜索栏 */}
      <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/')}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>

            <div className="flex-1 max-w-2xl">
              <SearchInput
                onSearch={handleNewSearch}
                placeholder={query}
                className="w-full"
              />
            </div>

            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span>找到 {results.length} 个结果</span>
              <span>•</span>
              <span>{(searchTime / 1000).toFixed(1)}秒</span>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-6 py-6">
        {/* 主要内容区域 */}
        <main className="max-w-4xl mx-auto">
            {/* 搜索结果标题 */}
            <div className="mb-6">
              <h1 className="text-2xl font-semibold text-gray-800 mb-2">
                关于 "{query}" 的智能分析结果
              </h1>
              <p className="text-gray-600">
                通过 Grok AI 智能搜索，我们为你找到了 {results.length} 个高质量回答，每个都经过了评论者可信度分析
              </p>
              <div className="flex items-center text-sm text-blue-600 mt-2">
                <span className="bg-blue-50 px-3 py-1 rounded-full">
                  ✨ 由 Grok AI 提供结构化搜索和分析
                </span>
              </div>
            </div>

            {/* 结果列表 */}
            {results.length > 0 ? (
              <div className="space-y-6">
                {results.map((result, index) => (
                  <div key={result.id} className="animate-fade-in" style={{ animationDelay: `${index * 100}ms` }}>
                    <ResultCard
                      result={result}
                      onViewDetail={handleViewDetail}
                      onBookmark={handleBookmark}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <AlertCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">暂无结果</h3>
                <p className="text-gray-500">没有找到符合条件的回答，请重新搜索</p>
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  )
}

export default ResultsPage 