#!/usr/bin/env python3
"""
CogBridges API 应用
独立的Flask API服务器，包含所有API路由定义
"""

import time
import asyncio
import threading
from flask import Flask, jsonify, request
from flask_cors import CORS
import atexit

from utils.logger_utils import get_logger
from services.cogbridges_service import CogBridgesService

# 全局服务实例
cogbridges_service = CogBridgesService()
logger = get_logger(__name__)

# 全局搜索状态存储
search_status = {}

def async_save_to_database(result):
    """异步保存搜索结果到数据库"""
    def save_task():
        try:
            logger.info(f"开始异步保存搜索结果到数据库: {result.session_id}")

            # 创建新的事件循环来运行异步保存方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                service = get_cogbridges_service()
                if service:
                    # 调用异步保存方法
                    loop.run_until_complete(service.save_search_result_async(result))
                    logger.info(f"搜索结果已异步保存: {result.session_id}")
                else:
                    logger.warning("CogBridges服务不可用，无法保存到数据库")
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"异步保存到数据库失败: {e}")

    # 在后台线程中执行保存任务
    thread = threading.Thread(target=save_task, daemon=True)
    thread.start()

def cleanup_service():
    """清理服务资源"""
    logger.info("正在关闭CogBridges服务...")
    loop = asyncio.get_event_loop()
    if loop.is_running():
        loop.create_task(cogbridges_service.close())
    else:
        asyncio.run(cogbridges_service.close())
    logger.info("CogBridges服务已关闭")

atexit.register(cleanup_service)

def get_cogbridges_service():
    """获取CogBridges服务实例"""
    return cogbridges_service

def update_search_status(session_id, status, progress=0, error=None, result=None):
    """更新搜索状态"""
    search_status[session_id] = {
        'status': status,  # 'running', 'completed', 'error'
        'progress': progress,
        'error': error,
        'result': result,
        'timestamp': time.time()
    }

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    CORS(app)
    
    @app.route('/test', methods=['GET'])
    def test():
        """简单测试接口"""
        return jsonify({
            "message": "Flask app is running",
            "timestamp": time.time(),
            "cogbridges_service_status": "initialized" if get_cogbridges_service() else "not_initialized"
        })
    
    @app.route('/api/health', methods=['GET'])
    def health_check():
        """健康检查接口"""
        try:
            return jsonify({
                "status": "healthy",
                "service": "CogBridges API",
                "timestamp": time.time()
            })
        except Exception as e:
            return jsonify({
                "status": "error",
                "error": str(e)
            }), 500

    @app.route('/api/search/progress/<session_id>', methods=['GET'])
    def get_search_progress(session_id):
        """获取搜索进度接口"""
        try:
            if session_id not in search_status:
                return jsonify({
                    "success": False,
                    "error": "会话不存在"
                }), 404

            status_info = search_status[session_id]
            return jsonify({
                "success": True,
                "session_id": session_id,
                "status": status_info['status'],
                "progress": status_info['progress'],
                "error": status_info.get('error'),
                "result": status_info.get('result'),
                "timestamp": status_info['timestamp']
            })

        except Exception as e:
            logger.error(f"获取搜索进度失败: {e}")
            return jsonify({
                "success": False,
                "error": f"获取搜索进度失败: {str(e)}"
            }), 500
    
    @app.route('/api/search', methods=['POST'])
    def search():
        """统一搜索接口 - 默认使用增强功能"""
        service = get_cogbridges_service()
        try:
            # 检查服务是否可用
            if not service:
                return jsonify({
                    "success": False,
                    "error": "CogBridges服务未初始化"
                }), 503
            
            data = request.get_json()
            query = data.get('query', '')

            if not query:
                return jsonify({
                    "success": False,
                    "error": "查询参数不能为空"
                }), 400

            # 生成会话ID
            session_id = f"search_{int(time.time() * 1000)}"
            
            # 初始化搜索状态
            update_search_status(session_id, 'running', progress=0)

            # 在后台线程中执行搜索
            def search_task():
                try:
                    # 创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        # 更新进度到20%
                        update_search_status(session_id, 'running', progress=20)
                        
                        # 执行搜索（包含LLM分析），但不保存到数据库
                        result = loop.run_until_complete(
                            service.search(query, save_to_db=False)
                        )

                        # 更新进度到80%
                        update_search_status(session_id, 'running', progress=80)

                        # 转换结果为JSON格式
                        response_data = {
                            "success": result.success,
                            "query": result.query,
                            "translated_query": result.translated_query,
                            "session_id": result.session_id,
                            "timestamp": result.timestamp.isoformat(),
                            "total_time": result.total_time,
                            "google_results": result.google_results,
                            "reddit_posts": result.reddit_posts,
                            "commenters_history": result.commenters_history,
                            "llm_analysis": result.llm_analysis,
                            "statistics": {
                                "translation_time": result.translation_time,
                                "google_search_time": result.google_search_time,
                                "reddit_posts_time": result.reddit_posts_time,
                                "commenters_history_time": result.commenters_history_time,
                                "llm_analysis_time": result.llm_analysis_time,
                                "google_results_count": len(result.google_results) if result.google_results else 0,
                                "reddit_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                                "commenters_count": len(result.commenters_history) if result.commenters_history else 0,
                                "similarity_analysis_count": len(result.llm_analysis.get("similarity_analysis", {})) if result.llm_analysis else 0,
                                "motivation_analysis_count": sum(len(motivations) for motivations in result.llm_analysis.get("motivation_analysis", {}).values()) if result.llm_analysis else 0
                            }
                        }

                        # 判断搜索是否有有效结果，而不仅仅看success标志
                        has_valid_results = False
                        # 如果至少有Reddit帖子（不要求评论），或至少有Google结果，也认为有有效结果
                        if result.reddit_posts and len(result.reddit_posts) > 0:
                            has_valid_results = True
                        elif result.google_results and len(result.google_results) > 0:
                            has_valid_results = True
                        
                        if not result.success and not has_valid_results:
                            # 完全失败：没有任何有效结果
                            response_data["error"] = result.error_message or "搜索未找到有效结果"
                            update_search_status(session_id, 'error', progress=100, error=response_data["error"])
                        else:
                            # 成功或部分成功：有有效结果或标记为成功
                            if not result.success:
                                # 部分成功：有结果但过程中有错误
                                response_data["warning"] = result.error_message
                                logger.warning(f"搜索部分成功，有警告: {result.error_message}")
                            
                            # 更新进度到100%并保存结果
                            update_search_status(session_id, 'completed', progress=100, result=response_data)
                            
                            # 启动异步保存到数据库（不阻塞响应）
                            async_save_to_database(result)

                    finally:
                        loop.close()

                except Exception as e:
                    logger.error(f"搜索任务执行失败: {e}")
                    update_search_status(session_id, 'error', progress=100, error=str(e))

            # 启动后台搜索任务
            thread = threading.Thread(target=search_task, daemon=True)
            thread.start()

            # 立即返回会话ID，让前端可以轮询进度
            return jsonify({
                "success": True,
                "session_id": session_id,
                "message": "搜索已开始，请轮询进度",
                "poll_url": f"/api/search/progress/{session_id}"
            })

        except Exception as e:
            logger.error(f"搜索请求处理失败: {e}")
            return jsonify({
                "success": False,
                "error": f"服务器内部错误: {str(e)}"
            }), 500

    @app.route('/api/history', methods=['GET'])
    def get_search_history():
        """获取搜索历史接口"""
        try:
            service = get_cogbridges_service()
            if not service:
                return jsonify({
                    "success": False,
                    "error": "CogBridges服务未初始化"
                }), 503

            # 获取查询参数
            limit = request.args.get('limit', 50, type=int)

            # 从数据服务获取会话列表
            sessions = service.data_service.list_sessions(limit=limit)

            # 转换为前端需要的格式
            history_data = []
            for session in sessions:
                history_item = {
                    "id": session.get("session_id", ""),
                    "query": session.get("query", ""),
                    "timestamp": session.get("timestamp", ""),
                    "source": session.get("source", "unknown"),
                    "success": session.get("success", True)
                }

                # 添加统计信息
                if session.get("source") == "database":
                    history_item.update({
                        "google_results_count": session.get("google_results_count", 0),
                        "reddit_posts_count": session.get("reddit_posts_count", 0),
                        "reddit_comments_count": session.get("reddit_comments_count", 0),
                        "llm_analysis_success": session.get("llm_analysis_success", False),
                        "llm_analysis_time": session.get("llm_analysis_time", 0.0),
                        "created_at": session.get("created_at", ""),
                        "updated_at": session.get("updated_at", "")
                    })
                else:
                    # JSON文件格式
                    stats = session.get("statistics", {})
                    history_item.update({
                        "google_results_count": stats.get("google_results_count", 0),
                        "reddit_posts_count": stats.get("reddit_posts_count", 0),
                        "reddit_comments_count": stats.get("reddit_comments_count", 0),
                        "file_size": session.get("file_size", 0),
                        "modified_time": session.get("modified_time", "")
                    })

                history_data.append(history_item)

            return jsonify({
                "success": True,
                "data": history_data,
                "total": len(history_data)
            })

        except Exception as e:
            logger.error(f"获取搜索历史失败: {e}")
            return jsonify({
                "success": False,
                "error": f"获取搜索历史失败: {str(e)}"
            }), 500

    @app.route('/api/bookmarks', methods=['GET'])
    def get_bookmarks():
        """获取收藏列表接口"""
        try:
            service = get_cogbridges_service()
            if not service:
                return jsonify({
                    "success": False,
                    "error": "CogBridges服务未初始化"
                }), 503

            # 获取查询参数
            limit = request.args.get('limit', 50, type=int)

            # 从数据库获取收藏列表
            if service.data_service.database_service and service.data_service.database_service.is_available():
                bookmarks_data = service.data_service.database_service.get_bookmarks(limit=limit)
                return jsonify({
                    "success": True,
                    "data": bookmarks_data,
                    "total": len(bookmarks_data)
                })
            else:
                # 数据库不可用时返回空列表
                return jsonify({
                    "success": True,
                    "data": [],
                    "total": 0,
                    "message": "数据库不可用"
                })

        except Exception as e:
            logger.error(f"获取收藏列表失败: {e}")
            return jsonify({
                "success": False,
                "error": f"获取收藏列表失败: {str(e)}"
            }), 500

    @app.route('/api/bookmarks', methods=['POST'])
    def add_bookmark():
        """添加收藏接口"""
        try:
            service = get_cogbridges_service()
            if not service:
                return jsonify({
                    "success": False,
                    "error": "CogBridges服务未初始化"
                }), 503

            data = request.get_json()
            if not data:
                return jsonify({
                    "success": False,
                    "error": "请求数据不能为空"
                }), 400

            # 从请求数据中提取收藏信息
            bookmark_data = {
                "session_id": data.get("session_id", ""),
                "comment_id": data.get("comment_id"),
                "query": data.get("query", ""),
                "title": data.get("title", ""),
                "content": data.get("content", ""),
                "author": data.get("author", ""),
                "subreddit": data.get("subreddit"),
                "score": data.get("score", 0),
                "url": data.get("url"),
                "tags": data.get("tags", []),
                "category": data.get("category")
            }

            # 保存到数据库
            if service.data_service.database_service and service.data_service.database_service.is_available():
                bookmark_id = service.data_service.database_service.add_bookmark(bookmark_data)
                if bookmark_id:
                    return jsonify({
                        "success": True,
                        "bookmark_id": bookmark_id,
                        "message": "收藏添加成功"
                    })
                else:
                    return jsonify({
                        "success": False,
                        "error": "收藏保存失败"
                    }), 500
            else:
                return jsonify({
                    "success": False,
                    "error": "数据库不可用"
                }), 503

        except Exception as e:
            logger.error(f"添加收藏失败: {e}")
            return jsonify({
                "success": False,
                "error": f"添加收藏失败: {str(e)}"
            }), 500

    @app.route('/api/bookmarks/<bookmark_id>', methods=['DELETE'])
    def remove_bookmark(bookmark_id):
        """删除收藏接口"""
        try:
            service = get_cogbridges_service()
            if not service:
                return jsonify({
                    "success": False,
                    "error": "CogBridges服务未初始化"
                }), 503

            # 从数据库删除收藏
            if service.data_service.database_service and service.data_service.database_service.is_available():
                success = service.data_service.database_service.remove_bookmark(bookmark_id)
                if success:
                    return jsonify({
                        "success": True,
                        "message": f"收藏 {bookmark_id} 删除成功"
                    })
                else:
                    return jsonify({
                        "success": False,
                        "error": f"收藏 {bookmark_id} 不存在或删除失败"
                    }), 404
            else:
                return jsonify({
                    "success": False,
                    "error": "数据库不可用"
                }), 503

        except Exception as e:
            logger.error(f"删除收藏失败: {e}")
            return jsonify({
                "success": False,
                "error": f"删除收藏失败: {str(e)}"
            }), 500

    @app.route('/api/status', methods=['GET'])
    def get_status():
        """获取服务状态接口"""
        service = get_cogbridges_service()
        try:
            if not service:
                return jsonify({
                    "service": "CogBridges API",
                    "timestamp": time.time(),
                    "status": "service_unavailable",
                    "error": "CogBridges服务未初始化"
                }), 503
            
            stats = service.get_statistics()

            # 检查各个服务的状态
            status_info = {
                "service": "CogBridges API",
                "timestamp": time.time(),
                "version": "2.0.0",
                "features": {
                    "basic_search": True,
                    "enhanced_comments": True,
                    "llm_analysis": True
                },
                "services": {
                    "google_search": bool(stats.get("google_stats", {}).get("configured", False)),
                    "reddit_api": bool(stats.get("reddit_stats", {}).get("configured", False)),
                    "llm_service": bool(stats.get("llm_stats", {}).get("configured", False))
                },
                "statistics": stats
            }

            return jsonify(status_info)

        except Exception as e:
            logger.error(f"状态查询失败: {e}")
            return jsonify({
                "service": "CogBridges API",
                "timestamp": time.time(),
                "error": f"状态查询失败: {str(e)}"
            }), 500

    @app.route('/api/sessions/<session_id>', methods=['GET'])
    def get_session_detail(session_id):
        """获取会话详细信息接口"""
        try:
            service = get_cogbridges_service()
            if not service:
                return jsonify({
                    "success": False,
                    "error": "CogBridges服务未初始化"
                }), 503

            # 从数据服务加载会话数据
            session_data = service.data_service.load_session_data(session_id)

            if not session_data:
                return jsonify({
                    "success": False,
                    "error": "会话不存在"
                }), 404

            return jsonify({
                "success": True,
                "data": session_data
            })

        except Exception as e:
            logger.error(f"获取会话详情失败: {e}")
            return jsonify({
                "success": False,
                "error": f"获取会话详情失败: {str(e)}"
            }), 500

    @app.route('/api/sessions/<session_id>', methods=['DELETE'])
    def delete_session(session_id):
        """删除会话接口"""
        try:
            service = get_cogbridges_service()
            if not service:
                return jsonify({
                    "success": False,
                    "error": "CogBridges服务未初始化"
                }), 503

            # 删除会话数据
            success = service.data_service.delete_session(session_id)

            if success:
                return jsonify({
                    "success": True,
                    "message": "会话删除成功"
                })
            else:
                return jsonify({
                    "success": False,
                    "error": "会话不存在或删除失败"
                }), 404

        except Exception as e:
            logger.error(f"删除会话失败: {e}")
            return jsonify({
                "success": False,
                "error": f"删除会话失败: {str(e)}"
            }), 500

    @app.route('/api/storage/statistics', methods=['GET'])
    def get_storage_statistics():
        """获取存储统计信息接口"""
        try:
            service = get_cogbridges_service()
            if not service:
                return jsonify({
                    "success": False,
                    "error": "CogBridges服务未初始化"
                }), 503

            # 获取存储统计信息
            stats = service.data_service.get_storage_statistics()

            return jsonify({
                "success": True,
                "data": stats
            })

        except Exception as e:
            logger.error(f"获取存储统计失败: {e}")
            return jsonify({
                "success": False,
                "error": f"获取存储统计失败: {str(e)}"
            }), 500

    @app.route('/api/database/migrate', methods=['POST'])
    def migrate_to_database():
        """迁移JSON数据到数据库接口"""
        try:
            service = get_cogbridges_service()
            if not service:
                return jsonify({
                    "success": False,
                    "error": "CogBridges服务未初始化"
                }), 503

            # 获取请求参数
            data = request.get_json() or {}
            session_id = data.get('session_id')  # 可选，指定会话ID

            # 执行迁移
            results = service.data_service.migrate_json_to_database(session_id)

            if "error" in results:
                return jsonify({
                    "success": False,
                    "error": results["error"]
                }), 400

            return jsonify({
                "success": True,
                "data": results
            })

        except Exception as e:
            logger.error(f"数据迁移失败: {e}")
            return jsonify({
                "success": False,
                "error": f"数据迁移失败: {str(e)}"
            }), 500

    @app.route('/api/sessions/<session_id>/llm-analysis', methods=['GET'])
    def get_session_llm_analysis(session_id):
        """获取会话的LLM分析数据接口"""
        try:
            service = get_cogbridges_service()
            if not service:
                return jsonify({
                    "success": False,
                    "error": "CogBridges服务未初始化"
                }), 503

            # 从数据服务加载会话数据
            session_data = service.data_service.load_session_data(session_id)

            if not session_data:
                return jsonify({
                    "success": False,
                    "error": "会话不存在"
                }), 404

            # 提取LLM分析数据
            llm_analysis = session_data.get('llm_analysis', {})

            if not llm_analysis:
                return jsonify({
                    "success": False,
                    "error": "该会话没有LLM分析数据"
                }), 404

            # 构建响应数据
            response_data = {
                "session_id": session_id,
                "llm_analysis": llm_analysis,
                "summary": {
                    "analysis_success": llm_analysis.get('success', False),
                    "analysis_time": llm_analysis.get('analysis_time', 0.0),
                    "similarity_users_count": len(llm_analysis.get('similarity_analysis', {})),
                    "motivation_users_count": len(llm_analysis.get('motivation_analysis', {})),
                    "total_motivation_analyses": sum(
                        len(analyses) for analyses in llm_analysis.get('motivation_analysis', {}).values()
                    )
                }
            }

            return jsonify({
                "success": True,
                "data": response_data
            })

        except Exception as e:
            logger.error(f"获取LLM分析数据失败: {e}")
            return jsonify({
                "success": False,
                "error": f"获取LLM分析数据失败: {str(e)}"
            }), 500

    return app


def run_app(host="localhost", port=5000, debug=False):
    """运行Flask应用"""
    app = create_app()
    app.run(host=host, port=port, debug=debug, threaded=True)


if __name__ == "__main__":
    from config import config
    run_app(host=config.HOST, port=config.PORT, debug=False) 